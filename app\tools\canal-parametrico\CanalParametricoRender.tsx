// src/components/CanalParametricoRender.tsx

import React, { useEffect, useRef, useState, useCallback } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
// import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader.js"; // No se usa
import { Water } from "three/examples/jsm/objects/Water2"; // PBR Water
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass";

// --- Import Types and Utilities from CanalParametrico and Utils ---
import { CanalShape } from "./CanalParametrico";
import {
  smootherstep,
  TransitionParams,
  buildChannelTransition,
} from "./utils/canalGeometryUtils";

// --- Constants ---
const TRANSITION_SEGMENTS = 25; // Más segmentos para transiciones más suaves (estilo Revit)
const MIN_DEPTH_SOLVER = 1e-5; // Shared constant
const GRAVITY = 9.81; // Shared constant
const TEXTURE_PATH = "/textures/"; // Base path in public folder
const CONCRETE_TEXTURE_PATH = `${TEXTURE_PATH}Concreto_4K-JPG/`;
const TABLET_BREAKPOINT = 1024; // Usamos 1024px como límite para asegurar que funcione correctamente

// --- Copied/Local Utility Functions & Types ---
// (These should ideally be in a shared utils file)

export interface ChannelSegmentParams {
  // Define locally as it's used by buildChannelSegment
  shape: CanalShape;
  width: number; // B (Base width)
  depth: number; // H (Total channel depth)
  sideSlope: number; // z (H:V)
  wallThickness: number;
}

/** Limpia recursivamente un objeto 3D y sus hijos, liberando geometría y materiales */
function disposeScene(obj: THREE.Object3D | null) {
  if (!obj) return;

  // Primero procesar todos los hijos recursivamente
  while (obj.children.length > 0) {
    disposeScene(obj.children[0]);
  }

  // Función auxiliar para limpiar texturas de un material
  const disposeMaterialTextures = (material: THREE.Material) => {
    // Usar una interfaz para representar material con texturas
    interface MaterialWithTextures {
      [key: string]: any; // Cambiar a any para permitir diferentes tipos
      uniforms?: {
        [key: string]: {
          value: THREE.Texture | { dispose?: () => void } | unknown;
        };
      };
    }

    const mat = material as unknown as MaterialWithTextures;
    // Limpiar todas las texturas posibles
    const texturesToDispose = [
      "map",
      "normalMap",
      "roughnessMap",
      "aoMap",
      "metalnessMap",
      "emissiveMap",
      "bumpMap",
      "displacementMap",
      "alphaMap",
      "lightMap",
      "clearcoatMap",
      "clearcoatNormalMap",
      "clearcoatRoughnessMap",
      "envMap",
      "specularMap",
    ];

    texturesToDispose.forEach((texProp) => {
      if (mat[texProp]) {
        const texture = mat[texProp] as THREE.Texture;
        texture.dispose();
        mat[texProp] = null;
      }
    });

    // Manejar uniforms para ShaderMaterial/Water
    if (mat.uniforms) {
      Object.values(mat.uniforms).forEach((uniform) => {
        const value = uniform.value;
        if (
          value &&
          typeof (value as { dispose?: () => void }).dispose === "function"
        ) {
          (value as { dispose: () => void }).dispose();
          uniform.value = null;
        }
      });
    }

    // Finalmente, disponer el material mismo
    material.dispose();
  };

  // Limpiar según el tipo de objeto
  if (obj instanceof THREE.Mesh) {
    // Limpiar geometría
    if (obj.geometry) {
      obj.geometry.dispose();
      obj.geometry = null as unknown as THREE.BufferGeometry;
    }

    // Limpiar materiales
    if (Array.isArray(obj.material)) {
      obj.material.forEach(disposeMaterialTextures);
    } else if (obj.material) {
      disposeMaterialTextures(obj.material);
    }
  } else if (obj instanceof THREE.Line) {
    if (obj.geometry) {
      obj.geometry.dispose();
      obj.geometry = null as unknown as THREE.BufferGeometry;
    }
    if (Array.isArray(obj.material)) {
      obj.material.forEach(disposeMaterialTextures);
    } else if (obj.material) {
      disposeMaterialTextures(obj.material);
    }
  } else if (obj instanceof THREE.Light) {
    // Limpiar mapas de sombras
    const light = obj as THREE.DirectionalLight;
    if (light.shadow?.map) {
      light.shadow.map.dispose();
      light.shadow.map = null as unknown as THREE.WebGLRenderTarget;
    }
  } else if (obj instanceof Water) {
    // Manejo especial para Water2 que tiene objetos internos
    try {
      // Intentar acceder a propiedades internas de Water2
      interface WaterMaterial {
        uniforms?: {
          [key: string]: {
            value: THREE.Texture | { dispose?: () => void } | unknown;
          };
        };
      }

      const waterMat = obj.material as unknown as WaterMaterial;
      if (waterMat.uniforms) {
        // Limpiar texturas en uniforms
        Object.values(waterMat.uniforms).forEach((uniform) => {
          const value = uniform.value;
          if (
            value &&
            typeof (value as { dispose?: () => void }).dispose === "function"
          ) {
            (value as { dispose: () => void }).dispose();
          }
        });
      }
      // Limpiar renderTargets específicos de Water2
      if (waterMat.uniforms?.tReflectionMap?.value) {
        const reflectionMap = waterMat.uniforms.tReflectionMap.value as {
          dispose?: () => void;
        };
        if (reflectionMap && typeof reflectionMap.dispose === "function") {
          reflectionMap.dispose();
        }
      }
      if (waterMat.uniforms?.tRefractionMap?.value) {
        const refractionMap = waterMat.uniforms.tRefractionMap.value as {
          dispose?: () => void;
        };
        if (refractionMap && typeof refractionMap.dispose === "function") {
          refractionMap.dispose();
        }
      }
    } catch (e) {
      console.warn("Error al limpiar objeto Water:", e);
    }
  }

  // Desconectar del grafo de escena
  obj.removeFromParent();
}

/** Calcula las propiedades hidráulicas para una sección y tirante dados (simplified for T calc) */
function calculateHydraulics(
  h: number,
  B: number,
  m: number,
  shape: CanalShape
): { T: number } {
  let T = 0;
  h = Math.max(h, 0);
  if (shape === "rectangular") {
    B = Math.max(B, 0);
    T = B;
  } else if (shape === "trapezoidal") {
    B = Math.max(B, 0);
    m = Math.max(m, 0);
    T = B + 2 * m * h;
  } else {
    m = Math.max(m, 1e-6);
    B = 0;
    T = 2 * m * h;
  }
  return { T }; // Only need Top Width (T) for water plane geometry here
}

/** Construye la geometría 3D para un segmento del canal */
function buildChannelSegment(
  params: ChannelSegmentParams,
  length: number,
  material: THREE.MeshStandardMaterial, // Material base (se clonará)
  textureSegmentSize: number = 2.0 // Tamaño de segmento para texturas (2m por defecto)
): THREE.Group {
  const segmentGroup = new THREE.Group();
  if (length <= 1e-6) return segmentGroup;

  const { shape, width, depth, sideSlope: m_param, wallThickness: wt } = params;
  const m = shape === "rectangular" ? 0 : Math.max(m_param, 0.01);
  const B =
    shape === "rectangular" || shape === "trapezoidal" ? Math.max(width, 0) : 0;
  const H = Math.max(depth, 0.1);
  const WT = Math.max(wt, 0.05);
  // Configurar extrusión con segmentación adecuada para texturas
  const extrudeSettings = {
    depth: length,
    bevelEnabled: false,
    steps: Math.max(1, Math.ceil(length / textureSegmentSize)), // Segmentar cada 2m
  };
  const halfLength = length / 2;

  if (shape === "rectangular") {
    // Calcular segmentos para mantener texturas de 2m
    const baseWidthSegments = Math.max(
      1,
      Math.ceil((B + 2 * WT) / textureSegmentSize)
    );
    const baseDepthSegments = Math.max(1, Math.ceil(WT / textureSegmentSize));
    const baseLengthSegments = Math.max(
      1,
      Math.ceil(length / textureSegmentSize)
    );

    // Crear geometría de la base con segmentación adecuada
    const baseGeo = new THREE.BoxGeometry(
      B + 2 * WT,
      WT,
      length,
      baseWidthSegments,
      baseDepthSegments,
      baseLengthSegments
    );

    // Clonar material y ajustar repetición de textura
    const baseMaterial = material.clone();
    if (baseMaterial.map) {
      // Configurar repetición de textura para la base
      baseMaterial.map.wrapS = baseMaterial.map.wrapT = THREE.RepeatWrapping;
      baseMaterial.map.repeat.set(
        Math.max(2, Math.ceil((B + 2 * WT) / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    // Configurar normal map y roughness map si existen
    if (baseMaterial.normalMap) {
      baseMaterial.normalMap.wrapS = baseMaterial.normalMap.wrapT =
        THREE.RepeatWrapping;
      baseMaterial.normalMap.repeat.set(
        Math.max(2, Math.ceil((B + 2 * WT) / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
      // Ajustar intensidad del normal map
      baseMaterial.normalScale.set(1.0, 1.0);
    }

    if (baseMaterial.roughnessMap) {
      baseMaterial.roughnessMap.wrapS = baseMaterial.roughnessMap.wrapT =
        THREE.RepeatWrapping;
      baseMaterial.roughnessMap.repeat.set(
        Math.max(2, Math.ceil((B + 2 * WT) / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    const baseMesh = new THREE.Mesh(baseGeo, baseMaterial);
    baseMesh.position.set(0, -WT / 2, 0);
    baseMesh.name = "base";
    segmentGroup.add(baseMesh);

    // Paredes laterales
    const wallWidthSegments = Math.max(1, Math.ceil(WT / textureSegmentSize));
    const wallHeightSegments = Math.max(1, Math.ceil(H / textureSegmentSize));
    const wallLengthSegments = Math.max(
      1,
      Math.ceil(length / textureSegmentSize)
    );

    const wallGeo = new THREE.BoxGeometry(
      WT,
      H,
      length,
      wallWidthSegments,
      wallHeightSegments,
      wallLengthSegments
    );

    // Pared izquierda
    const leftWallMaterial = material.clone();
    if (leftWallMaterial.map) {
      leftWallMaterial.map.wrapS = leftWallMaterial.map.wrapT =
        THREE.RepeatWrapping;
      leftWallMaterial.map.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    // Configurar normal map y roughness map si existen
    if (leftWallMaterial.normalMap) {
      leftWallMaterial.normalMap.wrapS = leftWallMaterial.normalMap.wrapT =
        THREE.RepeatWrapping;
      leftWallMaterial.normalMap.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
      // Ajustar intensidad del normal map
      leftWallMaterial.normalScale.set(1.0, 1.0);
    }

    if (leftWallMaterial.roughnessMap) {
      leftWallMaterial.roughnessMap.wrapS =
        leftWallMaterial.roughnessMap.wrapT = THREE.RepeatWrapping;
      leftWallMaterial.roughnessMap.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    const leftWall = new THREE.Mesh(wallGeo.clone(), leftWallMaterial);
    leftWall.position.set(-(B / 2) - WT / 2, H / 2, 0);
    leftWall.name = "leftWall";
    segmentGroup.add(leftWall);

    // Pared derecha
    const rightWallMaterial = material.clone();
    if (rightWallMaterial.map) {
      rightWallMaterial.map.wrapS = rightWallMaterial.map.wrapT =
        THREE.RepeatWrapping;
      rightWallMaterial.map.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    // Configurar normal map y roughness map si existen
    if (rightWallMaterial.normalMap) {
      rightWallMaterial.normalMap.wrapS = rightWallMaterial.normalMap.wrapT =
        THREE.RepeatWrapping;
      rightWallMaterial.normalMap.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
      // Ajustar intensidad del normal map
      rightWallMaterial.normalScale.set(1.0, 1.0);
    }

    if (rightWallMaterial.roughnessMap) {
      rightWallMaterial.roughnessMap.wrapS =
        rightWallMaterial.roughnessMap.wrapT = THREE.RepeatWrapping;
      rightWallMaterial.roughnessMap.repeat.set(
        Math.max(2, Math.ceil(H / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    const rightWall = new THREE.Mesh(wallGeo.clone(), rightWallMaterial);
    rightWall.position.set(B / 2 + WT / 2, H / 2, 0);
    rightWall.name = "rightWall";
    segmentGroup.add(rightWall);
  } else {
    // Trapezoidal or Triangular
    const i_bl = new THREE.Vector2(-B / 2, 0);
    const i_tl = new THREE.Vector2(-B / 2 - m * H, H);
    const i_br = new THREE.Vector2(B / 2, 0);
    const i_tr = new THREE.Vector2(B / 2 + m * H, H);
    const normFactor = Math.sqrt(1 + m * m);
    const perpOutL = new THREE.Vector2(
      1 / normFactor,
      m / normFactor
    ).multiplyScalar(WT);
    const perpOutR = new THREE.Vector2(
      -1 / normFactor,
      m / normFactor
    ).multiplyScalar(WT);
    const o_tl = i_tl.clone().add(perpOutL);
    const o_tr = i_tr.clone().add(perpOutR);
    const o_bl = i_bl.clone().add(perpOutL);
    const o_br = i_br.clone().add(perpOutR);

    if (shape === "trapezoidal" && B > 1e-6) {
      const baseShape = new THREE.Shape();
      baseShape.moveTo(o_bl.x, o_bl.y);
      baseShape.lineTo(o_br.x, o_br.y);
      baseShape.lineTo(i_br.x, i_br.y);
      baseShape.lineTo(i_bl.x, i_bl.y);
      baseShape.closePath();
      const baseGeo = new THREE.ExtrudeGeometry(baseShape, extrudeSettings);

      // Clonar material y ajustar repetición de textura para la base
      const baseMaterial = material.clone();
      if (baseMaterial.map) {
        const baseWidth = o_br.x - o_bl.x;
        baseMaterial.map.wrapS = baseMaterial.map.wrapT = THREE.RepeatWrapping;
        baseMaterial.map.repeat.set(
          Math.max(2, Math.ceil(baseWidth / textureSegmentSize)),
          Math.max(2, Math.ceil(length / textureSegmentSize))
        );
      }

      // Configurar normal map y roughness map si existen
      if (baseMaterial.normalMap) {
        baseMaterial.normalMap.wrapS = baseMaterial.normalMap.wrapT =
          THREE.RepeatWrapping;
        const baseWidth = o_br.x - o_bl.x;
        baseMaterial.normalMap.repeat.set(
          Math.max(2, Math.ceil(baseWidth / textureSegmentSize)),
          Math.max(2, Math.ceil(length / textureSegmentSize))
        );
        // Ajustar intensidad del normal map
        baseMaterial.normalScale.set(1.0, 1.0);
      }

      if (baseMaterial.roughnessMap) {
        baseMaterial.roughnessMap.wrapS = baseMaterial.roughnessMap.wrapT =
          THREE.RepeatWrapping;
        const baseWidth = o_br.x - o_bl.x;
        baseMaterial.roughnessMap.repeat.set(
          Math.max(2, Math.ceil(baseWidth / textureSegmentSize)),
          Math.max(2, Math.ceil(length / textureSegmentSize))
        );
      }

      const baseMesh = new THREE.Mesh(baseGeo, baseMaterial);
      baseMesh.name = "base";
      segmentGroup.add(baseMesh);
    }

    // Pared izquierda
    const leftWallShape = new THREE.Shape();
    leftWallShape.moveTo(i_bl.x, i_bl.y);
    leftWallShape.lineTo(i_tl.x, i_tl.y);
    leftWallShape.lineTo(o_tl.x, o_tl.y);
    leftWallShape.lineTo(o_bl.x, o_bl.y);
    leftWallShape.closePath();
    const leftWallGeo = new THREE.ExtrudeGeometry(
      leftWallShape,
      extrudeSettings
    );

    // Clonar material y ajustar repetición de textura para la pared izquierda
    const leftWallMaterial = material.clone();
    if (leftWallMaterial.map) {
      const wallHeight = i_tl.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tl.x - i_tl.x, 2) + Math.pow(o_tl.y - i_tl.y, 2)
      );
      leftWallMaterial.map.wrapS = leftWallMaterial.map.wrapT =
        THREE.RepeatWrapping;
      leftWallMaterial.map.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    // Configurar normal map y roughness map si existen
    if (leftWallMaterial.normalMap) {
      const wallHeight = i_tl.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tl.x - i_tl.x, 2) + Math.pow(o_tl.y - i_tl.y, 2)
      );
      leftWallMaterial.normalMap.wrapS = leftWallMaterial.normalMap.wrapT =
        THREE.RepeatWrapping;
      leftWallMaterial.normalMap.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
      // Ajustar intensidad del normal map
      leftWallMaterial.normalScale.set(1.0, 1.0);
    }

    if (leftWallMaterial.roughnessMap) {
      const wallHeight = i_tl.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tl.x - i_tl.x, 2) + Math.pow(o_tl.y - i_tl.y, 2)
      );
      leftWallMaterial.roughnessMap.wrapS =
        leftWallMaterial.roughnessMap.wrapT = THREE.RepeatWrapping;
      leftWallMaterial.roughnessMap.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    const leftWallMesh = new THREE.Mesh(leftWallGeo, leftWallMaterial);
    leftWallMesh.name = "leftWall";
    segmentGroup.add(leftWallMesh);

    // Pared derecha
    const rightWallShape = new THREE.Shape();
    rightWallShape.moveTo(i_br.x, i_br.y);
    rightWallShape.lineTo(i_tr.x, i_tr.y);
    rightWallShape.lineTo(o_tr.x, o_tr.y);
    rightWallShape.lineTo(o_br.x, o_br.y);
    rightWallShape.closePath();
    const rightWallGeo = new THREE.ExtrudeGeometry(
      rightWallShape,
      extrudeSettings
    );

    // Clonar material y ajustar repetición de textura para la pared derecha
    const rightWallMaterial = material.clone();
    if (rightWallMaterial.map) {
      const wallHeight = i_tr.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tr.x - i_tr.x, 2) + Math.pow(o_tr.y - i_tr.y, 2)
      );
      rightWallMaterial.map.wrapS = rightWallMaterial.map.wrapT =
        THREE.RepeatWrapping;
      rightWallMaterial.map.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    // Configurar normal map y roughness map si existen
    if (rightWallMaterial.normalMap) {
      const wallHeight = i_tr.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tr.x - i_tr.x, 2) + Math.pow(o_tr.y - i_tr.y, 2)
      );
      rightWallMaterial.normalMap.wrapS = rightWallMaterial.normalMap.wrapT =
        THREE.RepeatWrapping;
      rightWallMaterial.normalMap.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
      // Ajustar intensidad del normal map
      rightWallMaterial.normalScale.set(1.0, 1.0);
    }

    if (rightWallMaterial.roughnessMap) {
      const wallHeight = i_tr.y;
      const wallWidth = Math.sqrt(
        Math.pow(o_tr.x - i_tr.x, 2) + Math.pow(o_tr.y - i_tr.y, 2)
      );
      rightWallMaterial.roughnessMap.wrapS =
        rightWallMaterial.roughnessMap.wrapT = THREE.RepeatWrapping;
      rightWallMaterial.roughnessMap.repeat.set(
        Math.max(2, Math.ceil(wallWidth / textureSegmentSize)),
        Math.max(2, Math.ceil(length / textureSegmentSize))
      );
    }

    const rightWallMesh = new THREE.Mesh(rightWallGeo, rightWallMaterial);
    rightWallMesh.name = "rightWall";
    segmentGroup.add(rightWallMesh);

    segmentGroup.children.forEach((child) => {
      if (
        child instanceof THREE.Mesh &&
        child.geometry instanceof THREE.ExtrudeGeometry
      ) {
        child.position.z -= halfLength;
      }
    });
  }
  segmentGroup.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      child.castShadow = true;
      child.receiveShadow = true;
      child.position.z += halfLength;
    }
  });
  return segmentGroup;
}

// --- Fin Utilidades Copiadas ---

// --- Tipos Específicos del Renderizador ---
type TextureSet = "concrete" | "rock";
// Estado recibido desde CanalParametrico a través de react-router state
interface RenderState {
  // Parámetros básicos del canal
  shape: CanalShape;
  width: number; // B (Base width)
  depth: number; // H (Total channel depth)
  sideSlope: number; // z (H:V)
  wallThickness: number;

  // Parámetros adicionales
  canalLength: number;
  canalSlope: number;
  actualWaterDepth: number; // Tirante calculado/validado

  // Parámetros de transición
  enableTransition: boolean;
  transitionStart?: number;
  transitionLength?: number;
  endCanalShape?: CanalShape;
  endCanalWidth?: number;
  endCanalDepth?: number;
  endSideSlope?: number;

  // Preferencias de UI
  isDarkMode: boolean; // Sugerencia de tema
}

/** Construye la geometría 3D para el agua en un segmento del canal */
function buildWaterSegment(
  params: ChannelSegmentParams,
  waterDepth: number,
  length: number,
  material: THREE.Material,
  textureSegmentSize: number = 2.0
): THREE.Mesh | null {
  if (waterDepth <= MIN_DEPTH_SOLVER || length <= 1e-6) return null;

  const { shape, width, depth, sideSlope } = params;
  const h = Math.min(waterDepth, depth); // Limitar el tirante a la profundidad del canal

  // Calcular ancho superficial (T)
  const T = calculateHydraulics(h, width, sideSlope, shape).T;

  // Para todos los tipos de canal, usar un PlaneGeometry para el agua
  // Esto simplifica la geometría y mejora la aplicación de texturas
  const waterWidth = Math.max(T, 0.01); // Usar el ancho superficial calculado

  // Crear geometría con segmentación adecuada para texturas
  const widthSegments = Math.max(4, Math.ceil(waterWidth / textureSegmentSize));
  const lengthSegments = Math.max(4, Math.ceil(length / textureSegmentSize));

  const waterGeo = new THREE.PlaneGeometry(
    waterWidth,
    length,
    widthSegments,
    lengthSegments
  );

  // Ajustar UVs para aplicar textura correctamente en segmentos de 2m
  const uvAttribute = waterGeo.attributes.uv;
  const positions = waterGeo.attributes.position;

  for (let i = 0; i < uvAttribute.count; i++) {
    const x = positions.getX(i);
    const y = positions.getY(i);

    // Calcular UVs basadas en posición real y tamaño de segmento
    // Multiplicar por 2 para hacer la textura más densa
    const u = ((x + waterWidth / 2) / textureSegmentSize) * 2;
    const v = ((y + length / 2) / textureSegmentSize) * 2;

    uvAttribute.setXY(i, u, v);
  }
  uvAttribute.needsUpdate = true;

  // Crear el mesh con la geometría y material
  const waterMesh = new THREE.Mesh(waterGeo, material);

  // Orientar correctamente el plano de agua
  waterMesh.rotation.x = -Math.PI / 2; // Rotar para que quede horizontal
  waterMesh.position.y = h + 0.01; // Posicionar a la altura del tirante con un pequeño desplazamiento para evitar z-fighting
  waterMesh.name = "waterSegment";

  return waterMesh;
}

// --- Componente Renderizador ---
const CanalParametricoRender: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  // Estado interno para los parámetros validados
  const [renderState, setRenderState] = useState<RenderState | null>(null);
  const [stateError, setStateError] = useState<string | null>(null); // Error en los datos recibidos

  // --- Estado para detección de tamaño de pantalla ---
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== "undefined" ? window.innerWidth : 0
  );

  // Efecto para detectar cambios en el tamaño de la ventana
  useEffect(() => {
    // Función para actualizar el estado con el ancho actual
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Verificar el ancho inicial
    handleResize();

    // Agregar listener para cambios de tamaño
    window.addEventListener("resize", handleResize);

    // Limpiar listener al desmontar
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // --- Refs ---
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const composerRef = useRef<EffectComposer | null>(null);
  // Actualizado: cambiamos el tipo de waterRef para aceptar tanto Water como Mesh
  const waterRef = useRef<THREE.Mesh | Water | null>(null); // Ref para objeto de agua
  const requestRef = useRef<number>(); // Ref para cancelar requestAnimationFrame
  const canalRenderGroupRef = useRef<THREE.Group | null>(null); // Ref al grupo principal del canal renderizado

  // --- Estado Interno del Componente ---
  const [selectedTexture, setSelectedTexture] =
    useState<TextureSet>("concrete"); // Textura seleccionada
  const [texturesLoaded, setTexturesLoaded] = useState(false); // Flag carga texturas
  const [envMapLoaded, setEnvMapLoaded] = useState(false); // Flag carga HDRI
  const [sceneBuilt, setSceneBuilt] = useState(false); // Flag construcción escena
  const [loadingError, setLoadingError] = useState<string | null>(null); // Error durante carga de assets

  // --- Refs para Materiales (para evitar recarga innecesaria) ---
  const concreteMaterial = useRef<THREE.MeshStandardMaterial | null>(null);
  const rockMaterial = useRef<THREE.MeshStandardMaterial | null>(null);
  const grassMaterial = useRef<THREE.MeshStandardMaterial | null>(null);
  const waterNormalMap = useRef<THREE.Texture | null>(null);

  // --- Efecto 1: Validar Estado Recibido de la Ruta ---
  useEffect(() => {
    // Por ahora, crear un estado de ejemplo para que el componente funcione
    // En una implementación completa, esto vendría de los parámetros de la URL o del estado global
    const stateFromLocation: Partial<RenderState> = {
      shape: "rectangular",
      width: 2.0,
      depth: 1.5,
      sideSlope: 0,
      wallThickness: 0.2,
      canalLength: 10.0,
      canalSlope: 0.001,
      actualWaterDepth: 0.8,
      enableTransition: false,
      isDarkMode: false
    };

    console.log("Using example state:", stateFromLocation); // Log para depuración

    if (!stateFromLocation) {
      setStateError(
        "Error: No se recibieron datos del canal. Vuelve al editor."
      );
      setRenderState(null);
      return;
    }
    // Validación detallada
    const errors: string[] = [];

    // Validar parámetros básicos
    if (
      stateFromLocation.canalLength === undefined ||
      stateFromLocation.canalLength <= 0
    )
      errors.push("Longitud inválida");
    if (stateFromLocation.depth === undefined || stateFromLocation.depth <= 0)
      errors.push("Profundidad inicial inválida");
    if (stateFromLocation.width === undefined || stateFromLocation.width < 0)
      errors.push("Ancho base inválido");
    if (stateFromLocation.shape === undefined)
      errors.push("Forma de canal no especificada");
    if (
      stateFromLocation.sideSlope === undefined ||
      (stateFromLocation.shape !== "rectangular" &&
        stateFromLocation.sideSlope < 0)
    )
      errors.push("Pendiente lateral inválida");
    if (
      stateFromLocation.wallThickness === undefined ||
      stateFromLocation.wallThickness <= 0
    )
      errors.push("Espesor de pared inválido");
    if (stateFromLocation.canalSlope === undefined)
      errors.push("Pendiente longitudinal no especificada");

    // Mostrar en consola los datos recibidos para depuración
    console.log("Parámetros recibidos:", {
      shape: stateFromLocation.shape,
      width: stateFromLocation.width,
      depth: stateFromLocation.depth,
      sideSlope: stateFromLocation.sideSlope,
      wallThickness: stateFromLocation.wallThickness,
      canalLength: stateFromLocation.canalLength,
      canalSlope: stateFromLocation.canalSlope,
      actualWaterDepth: stateFromLocation.actualWaterDepth,
      enableTransition: stateFromLocation.enableTransition,
    });

    // Validar tirante
    if (
      stateFromLocation.actualWaterDepth === undefined ||
      stateFromLocation.actualWaterDepth < 0
    )
      errors.push("Tirante inválido");

    // Validar parámetros de transición si está habilitada
    if (stateFromLocation.enableTransition) {
      if (
        stateFromLocation.transitionStart === undefined ||
        stateFromLocation.transitionLength === undefined ||
        stateFromLocation.endCanalShape === undefined ||
        stateFromLocation.endCanalWidth === undefined ||
        stateFromLocation.endCanalDepth === undefined ||
        stateFromLocation.endSideSlope === undefined
      ) {
        errors.push("Faltan datos para la transición habilitada");
      } else if (
        stateFromLocation.transitionStart + stateFromLocation.transitionLength >
        (stateFromLocation.canalLength ?? 0) + 1e-6
      ) {
        errors.push("Transición excede longitud total");
      }
    }

    if (errors.length > 0) {
      setStateError(`Error en datos: ${errors.join(", ")}. Vuelve al editor.`);
      setRenderState(null);
      return;
    }

    // Estado válido, proceder
    setRenderState(stateFromLocation as RenderState);
    setStateError(null);
    // Resetear flags para forzar recarga/reconstrucción si el estado cambia
    setTexturesLoaded(false);
    setEnvMapLoaded(false);
    setSceneBuilt(false);
    setLoadingError(null);
    console.log("Render state validado:", stateFromLocation);
  }, []); // Solo ejecutar una vez al montar el componente

  // --- Efecto 2: Cargar Assets (Texturas, HDRI) ---
  useEffect(() => {
    // Solo ejecutar si hay estado válido y los assets no están ya cargados
    if (!renderState || (texturesLoaded && envMapLoaded)) return;

    console.log("Iniciando carga de assets...");
    let isMounted = true; // Flag para evitar actualizaciones si se desmonta durante la carga
    let texturesToLoadCount = 0;
    let texturesDoneCount = 0;
    let envMapDoneFlag = false;
    const loadingErrorsList: string[] = [];

    // Función para verificar si todo ha cargado
    const checkLoadingComplete = () => {
      if (!isMounted) return;
      if (texturesDoneCount >= texturesToLoadCount && envMapDoneFlag) {
        if (loadingErrorsList.length > 0) {
          setLoadingError(`Errores al cargar: ${loadingErrorsList.join(", ")}`);
        } else {
          setLoadingError(null);
        }
        setTexturesLoaded(true);
        setEnvMapLoaded(true);
        console.log("Carga de assets completada.");
      }
    };

    // Función para crear texturas procedurales de concreto
    const loadConcreteTextures = (
      materialRef: React.MutableRefObject<THREE.MeshStandardMaterial | null>
    ) => {
      // Incrementar contadores para mantener la lógica de carga
      texturesToLoadCount += 3; // Color, Normal, Roughness

      console.log("Creando texturas procedurales para el concreto...");

      // Crear textura de color para el concreto
      const colorCanvas = document.createElement("canvas");
      colorCanvas.width = 1024;
      colorCanvas.height = 1024;
      const colorCtx = colorCanvas.getContext("2d");

      if (colorCtx) {
        // Color base gris claro para el concreto
        colorCtx.fillStyle = "#aaaaaa";
        colorCtx.fillRect(0, 0, colorCanvas.width, colorCanvas.height);

        // Añadir variaciones de color para simular concreto
        for (let y = 0; y < colorCanvas.height; y += 4) {
          for (let x = 0; x < colorCanvas.width; x += 4) {
            // Variación aleatoria de color
            const variation = Math.floor(Math.random() * 30);
            const color = 170 - variation;
            colorCtx.fillStyle = `rgb(${color},${color},${color})`;
            colorCtx.fillRect(x, y, 4, 4);
          }
        }

        // Añadir algunas manchas y grietas
        for (let i = 0; i < 200; i++) {
          const x = Math.random() * colorCanvas.width;
          const y = Math.random() * colorCanvas.height;
          const size = 5 + Math.random() * 20;
          const darkness = 120 + Math.floor(Math.random() * 50);

          colorCtx.fillStyle = `rgb(${darkness},${darkness},${darkness})`;
          colorCtx.beginPath();
          colorCtx.arc(x, y, size, 0, Math.PI * 2);
          colorCtx.fill();
        }

        // Suavizar
        try {
          colorCtx.filter = "blur(1px)";
          colorCtx.drawImage(
            colorCanvas,
            0,
            0,
            colorCanvas.width,
            colorCanvas.height
          );
          colorCtx.filter = "none";
        } catch (e) {
          console.log("El navegador no soporta filtros en canvas");
        }
      }

      // Crear normal map procedural
      const normalCanvas = document.createElement("canvas");
      normalCanvas.width = 1024;
      normalCanvas.height = 1024;
      const normalCtx = normalCanvas.getContext("2d");

      if (normalCtx) {
        // Color base para normal map (128,128,255 representa superficie plana)
        normalCtx.fillStyle = "rgb(128,128,255)";
        normalCtx.fillRect(0, 0, normalCanvas.width, normalCanvas.height);

        // Añadir variaciones para simular rugosidad
        for (let y = 0; y < normalCanvas.height; y += 8) {
          for (let x = 0; x < normalCanvas.width; x += 8) {
            // Variación aleatoria para el normal map
            const varX = Math.floor(Math.random() * 30) - 15;
            const varY = Math.floor(Math.random() * 30) - 15;
            normalCtx.fillStyle = `rgb(${128 + varX},${128 + varY},255)`;
            normalCtx.fillRect(x, y, 8, 8);
          }
        }

        // Añadir algunas grietas y detalles
        for (let i = 0; i < 100; i++) {
          const x = Math.random() * normalCanvas.width;
          const y = Math.random() * normalCanvas.height;
          const length = 10 + Math.random() * 50;
          const angle = Math.random() * Math.PI * 2;

          normalCtx.strokeStyle = "rgb(100,100,255)";
          normalCtx.lineWidth = 2;
          normalCtx.beginPath();
          normalCtx.moveTo(x, y);
          normalCtx.lineTo(
            x + Math.cos(angle) * length,
            y + Math.sin(angle) * length
          );
          normalCtx.stroke();
        }

        // Suavizar
        try {
          normalCtx.filter = "blur(2px)";
          normalCtx.drawImage(
            normalCanvas,
            0,
            0,
            normalCanvas.width,
            normalCanvas.height
          );
          normalCtx.filter = "none";
        } catch (e) {
          console.log("El navegador no soporta filtros en canvas");
        }
      }

      // Crear mapa de rugosidad procedural
      const roughnessCanvas = document.createElement("canvas");
      roughnessCanvas.width = 1024;
      roughnessCanvas.height = 1024;
      const roughnessCtx = roughnessCanvas.getContext("2d");

      if (roughnessCtx) {
        // Base gris medio para rugosidad media
        roughnessCtx.fillStyle = "#888888";
        roughnessCtx.fillRect(
          0,
          0,
          roughnessCanvas.width,
          roughnessCanvas.height
        );

        // Añadir variaciones de rugosidad
        for (let y = 0; y < roughnessCanvas.height; y += 6) {
          for (let x = 0; x < roughnessCanvas.width; x += 6) {
            // Variación aleatoria para rugosidad
            const variation = Math.floor(Math.random() * 60) - 30;
            const value = Math.max(0, Math.min(255, 136 + variation));
            roughnessCtx.fillStyle = `rgb(${value},${value},${value})`;
            roughnessCtx.fillRect(x, y, 6, 6);
          }
        }

        // Añadir algunas áreas más lisas y más rugosas
        for (let i = 0; i < 150; i++) {
          const x = Math.random() * roughnessCanvas.width;
          const y = Math.random() * roughnessCanvas.height;
          const size = 10 + Math.random() * 30;
          // Alternar entre áreas más lisas y más rugosas
          const value = i % 2 === 0 ? 200 : 50;

          roughnessCtx.fillStyle = `rgb(${value},${value},${value})`;
          roughnessCtx.beginPath();
          roughnessCtx.arc(x, y, size, 0, Math.PI * 2);
          roughnessCtx.fill();
        }

        // Suavizar
        try {
          roughnessCtx.filter = "blur(3px)";
          roughnessCtx.drawImage(
            roughnessCanvas,
            0,
            0,
            roughnessCanvas.width,
            roughnessCanvas.height
          );
          roughnessCtx.filter = "none";
        } catch (e) {
          console.log("El navegador no soporta filtros en canvas");
        }
      }

      // Crear texturas desde los canvas
      const colorMap = new THREE.CanvasTexture(colorCanvas);
      const normalMap = new THREE.CanvasTexture(normalCanvas);
      const roughnessMap = new THREE.CanvasTexture(roughnessCanvas);

      // Configurar texturas para segmentos de 2m
      [colorMap, normalMap, roughnessMap].forEach((tex) => {
        if (!tex) return;
        tex.wrapS = tex.wrapT = THREE.RepeatWrapping; // Habilitar repetición
        // Configurar repetición para segmentos de 2m
        tex.repeat.set(4, 4); // Repetir varias veces para más detalle
        tex.anisotropy =
          rendererRef.current?.capabilities.getMaxAnisotropy() ?? 4;
        tex.generateMipmaps = true;
        tex.minFilter = THREE.LinearMipmapLinearFilter;
        tex.magFilter = THREE.LinearFilter;
        tex.needsUpdate = true;
      });

      colorMap.colorSpace = THREE.SRGBColorSpace;

      // Incrementar contador de texturas cargadas
      texturesDoneCount += 3;
      checkLoadingComplete();

      // Liberar material anterior si existe
      materialRef.current?.dispose();

      // Crear material con texturas
      materialRef.current = new THREE.MeshStandardMaterial({
        map: colorMap,
        normalMap: normalMap,
        roughnessMap: roughnessMap,
        roughness: 0.8,
        metalness: 0.05,
        side: THREE.FrontSide,
      });
    };

    // Función para crear material simple para la roca
    const createSimpleMaterial = (
      materialRef: React.MutableRefObject<THREE.MeshStandardMaterial | null>,
      color: string,
      roughness: number = 0.7,
      metalness: number = 0.1
    ) => {
      // Incrementar contadores para mantener la lógica de carga
      texturesToLoadCount += 1;

      // Liberar material anterior si existe
      materialRef.current?.dispose();

      // Crear material simple con color sólido
      materialRef.current = new THREE.MeshStandardMaterial({
        color: color,
        roughness: roughness,
        metalness: metalness,
        side: THREE.FrontSide,
      });

      // Incrementar contador de texturas cargadas
      texturesDoneCount += 1;
      checkLoadingComplete();
    };

    // Cargar texturas para el concreto
    loadConcreteTextures(concreteMaterial);

    // Crear material simple para la roca
    createSimpleMaterial(rockMaterial, "#777777", 0.9, 0.1); // Gris oscuro para roca

    // Crear material para la hierba
    createSimpleMaterial(grassMaterial, "#4CAF50", 0.9, 0.0); // Verde para hierba

    // Cargar textura real para el agua desde archivos
    texturesToLoadCount++;
    waterNormalMap.current?.dispose(); // Liberar anterior

    // Crear textura procedural para el agua (más confiable que cargar archivos externos)
    const canvas = document.createElement("canvas");
    canvas.width = 1024; // Mayor resolución para mejor calidad
    canvas.height = 1024;
    const ctx = canvas.getContext("2d");

    if (ctx) {
      // Crear un normal map para agua más realista
      // Fondo gris medio (128,128,255) para normal map
      ctx.fillStyle = "rgb(128,128,255)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Crear patrón de ondas horizontales (dirección principal del flujo)
      for (let y = 0; y < canvas.height; y += 8) {
        // Dibujar líneas onduladas horizontales
        ctx.beginPath();
        ctx.lineWidth = 1;

        // Alternar colores para crear efecto de relieve en normal map
        if (y % 16 === 0) {
          ctx.strokeStyle = "rgb(180,180,255)"; // Componente Z más alta (cresta)
        } else {
          ctx.strokeStyle = "rgb(100,100,255)"; // Componente Z más baja (valle)
        }

        for (let x = 0; x < canvas.width; x += 2) {
          // Crear ondas con diferentes frecuencias superpuestas
          const wave1 = 3 * Math.sin(x * 0.05 + y * 0.1);
          const wave2 = 2 * Math.sin(x * 0.02 - y * 0.05 + 0.3);
          const height = wave1 + wave2;

          if (x === 0) {
            ctx.moveTo(x, y + height);
          } else {
            ctx.lineTo(x, y + height);
          }
        }
        ctx.stroke();
      }

      // Añadir ondas diagonales para más detalle
      for (let i = 0; i < canvas.width * 2; i += 32) {
        ctx.beginPath();
        ctx.lineWidth = 1;
        ctx.strokeStyle = "rgba(140,140,255,0.5)";

        for (let j = 0; j < canvas.width; j += 2) {
          const x = j;
          const y = (i - j) % canvas.height;

          if (y >= 0 && y < canvas.height) {
            const wave = 2 * Math.sin(j * 0.03 + i * 0.01);

            if (j === 0 || y === 0) {
              ctx.moveTo(x, y + wave);
            } else {
              ctx.lineTo(x, y + wave);
            }
          }
        }
        ctx.stroke();
      }

      // Aplicar un ligero desenfoque para suavizar el patrón
      try {
        ctx.filter = "blur(1px)";
        ctx.drawImage(canvas, 0, 0, canvas.width, canvas.height);
        ctx.filter = "none";
      } catch (e) {
        console.log(
          "El navegador no soporta filtros en canvas, ignorando desenfoque"
        );
      }

      // Crear textura desde el canvas
      waterNormalMap.current = new THREE.CanvasTexture(canvas);
      waterNormalMap.current.wrapS = waterNormalMap.current.wrapT =
        THREE.RepeatWrapping;
      waterNormalMap.current.repeat.set(8, 8); // Repetir la textura para más detalle

      // Configurar propiedades adicionales para mejorar la apariencia
      waterNormalMap.current.anisotropy =
        rendererRef.current?.capabilities.getMaxAnisotropy() ?? 4;
      waterNormalMap.current.generateMipmaps = true;
      waterNormalMap.current.minFilter = THREE.LinearMipmapLinearFilter;
      waterNormalMap.current.magFilter = THREE.LinearFilter;
      waterNormalMap.current.needsUpdate = true;

      console.log("Textura de agua creada correctamente");
    } else {
      console.error(
        "No se pudo crear el contexto del canvas para la textura de agua"
      );
    }

    texturesDoneCount++;
    checkLoadingComplete();

    // Usar color sólido en lugar de HDRI (para evitar errores)
    envMapDoneFlag = true;
    if (isMounted && sceneRef.current) {
      // Usar color sólido como fondo
      sceneRef.current.background = new THREE.Color(
        renderState?.isDarkMode ? 0x1a1a1a : 0xaaaaaa
      );
      sceneRef.current.environment = null; // Sin mapa de entorno para reflejos
    }
    checkLoadingComplete();
    console.log("Usando fondo sólido en lugar de HDRI.");

    // Función de limpieza para este efecto
    return () => {
      isMounted = false;
      console.log("Limpieza efecto carga de assets.");
    };
  }, [renderState, texturesLoaded, envMapLoaded]); // Dependencias: estado y flags de carga

  // --- Setup Iluminación ---
  const setupLighting = useCallback(
    (scene: THREE.Scene, targetGroup: THREE.Group | null) => {
      // Eliminar luz anterior para evitar duplicados si se llama varias veces
      const existingLight = scene.getObjectByName("renderSunLight");
      if (existingLight) {
        // Verificar si es una luz direccional con target
        if (
          existingLight instanceof THREE.DirectionalLight &&
          existingLight.target
        ) {
          disposeScene(existingLight.target);
        }
        disposeScene(existingLight);
      }

      if (!renderState) return; // Necesita estado para posicionar la luz

      // Luz direccional principal (Sol) para sombras
      const sunLight = new THREE.DirectionalLight(0xffffff, 1.5); // Mayor intensidad
      sunLight.name = "renderSunLight";
      // Posición basada en dimensiones del canal
      sunLight.position.set(
        renderState.canalLength * 0.5,
        renderState.depth * 8,
        renderState.width * 2
      );
      sunLight.castShadow = false; // Desactivar sombras

      // Luz ambiental para iluminar las partes en sombra
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      ambientLight.name = "ambientLight";
      scene.add(ambientLight);

      // Ajustar cámara de sombras al tamaño del objeto
      if (targetGroup) {
        const bbox = new THREE.Box3().setFromObject(targetGroup);
        const size = bbox.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        const camSize = maxDim > 1 ? maxDim * 1.5 : 20; // Tamaño del frustum
        sunLight.shadow.camera.left = -camSize / 2;
        sunLight.shadow.camera.right = camSize / 2;
        sunLight.shadow.camera.top = camSize / 2;
        sunLight.shadow.camera.bottom = -camSize / 2;
        sunLight.shadow.camera.near = 0.5;
        sunLight.shadow.camera.far = maxDim * 3 + 50;
        sunLight.shadow.camera.updateProjectionMatrix(); // Actualizar cámara
        sunLight.target = targetGroup; // Apuntar al grupo
      } else {
        sunLight.target.position.set(0, 0, 0);
      } // Objetivo por defecto

      scene.add(sunLight);
      scene.add(sunLight.target); // Añadir objetivo a la escena también
    },
    [renderState]
  ); // Depende del estado para las dimensiones

  // --- Efecto 3: Construir Escena 3D (Geometría) ---
  // Función para construir una transición suave para canales rectangulares
  const buildSmoothRectangularTransition = (
    startParams: ChannelSegmentParams,
    endParams: ChannelSegmentParams,
    transitionLength: number,
    segmentMaterial: THREE.MeshStandardMaterial,
    parentGroup: THREE.Group
  ): THREE.Group => {
    // Crear un grupo para contener la transición
    const transitionGroup = new THREE.Group();
    parentGroup.add(transitionGroup);

    // Usar la función buildChannelTransition con los parámetros para canales rectangulares
    const transitionParams: TransitionParams = {
      startParams: startParams,
      endParams: endParams,
      numSegments: TRANSITION_SEGMENTS,
      transitionLength: transitionLength,
      material: segmentMaterial,
    };

    // Construir la transición utilizando la función de utils
    const channelTransition = buildChannelTransition(transitionParams);
    transitionGroup.add(channelTransition);

    return transitionGroup;
  };

  const buildRenderScene = useCallback(() => {
    // Verificar prerrequisitos
    if (
      !sceneRef.current ||
      !renderState ||
      !texturesLoaded ||
      !envMapLoaded ||
      !concreteMaterial.current ||
      !rockMaterial.current ||
      !grassMaterial.current
    ) {
      console.warn("Prerrequisitos para construir escena no cumplidos.");
      return;
    }
    // Evitar reconstrucción si ya está construida para el estado actual
    if (sceneBuilt) {
      console.log("Escena ya construida para este estado.");
      return;
    }

    console.log("Construyendo escena del render...");
    const scene = sceneRef.current;

    // Limpiar grupo anterior
    disposeScene(canalRenderGroupRef.current);

    // Crear nuevo grupo
    const renderCanalGroup = new THREE.Group();
    renderCanalGroup.name = "renderCanalGroup";
    scene.add(renderCanalGroup);
    canalRenderGroupRef.current = renderCanalGroup; // Guardar referencia al nuevo grupo

    // Seleccionar material base (será clonado para cada segmento)
    const baseMaterial =
      selectedTexture === "concrete"
        ? concreteMaterial.current
        : rockMaterial.current;
    if (!baseMaterial) {
      console.error("Material base no disponible!");
      return;
    }

    // Extraer parámetros del estado
    const {
      shape: canalShape,
      width: canalWidth,
      depth: canalDepth,
      canalLength,
      sideSlope,
      wallThickness,
      actualWaterDepth,
      enableTransition,
      transitionStart = 0,
      transitionLength = 0,
      endCanalShape = canalShape,
      endCanalWidth = canalWidth,
      endCanalDepth = canalDepth,
      endSideSlope = sideSlope,
    } = renderState;

    // --- Crear Terreno Alrededor del Canal ---
    // Calcular dimensiones del terreno basadas en el canal
    const terrainWidth = Math.max(canalWidth * 10, 20); // Ancho del terreno
    const terrainLength = canalLength * 1.2; // Largo del terreno
    const terrainHeight = 0.5; // Altura del terreno

    // Crear geometría del terreno
    const terrainGeometry = new THREE.BoxGeometry(
      terrainWidth,
      terrainHeight,
      terrainLength
    );

    // Crear malla del terreno con material de hierba
    const terrain = new THREE.Mesh(terrainGeometry, grassMaterial.current);
    terrain.position.y = -terrainHeight / 2; // Posicionar debajo del canal
    terrain.receiveShadow = true; // Recibir sombras
    terrain.name = "terrain";
    renderCanalGroup.add(terrain);

    // --- Crear el Canal ---
    // Grupo para contener la geometría del canal
    const canalGroup = new THREE.Group();
    canalGroup.name = "canalGroup";
    renderCanalGroup.add(canalGroup);

    // Material base para el canal
    const canalMaterial = baseMaterial.clone();

    // Verificar si hay transición habilitada
    if (enableTransition && transitionLength > 0) {
      // Crear parámetros de inicio y fin para la transición
      const startSegmentParams: ChannelSegmentParams = {
        shape: canalShape,
        width: canalWidth,
        depth: canalDepth,
        sideSlope: sideSlope,
        wallThickness: wallThickness,
      };

      const endSegmentParams: ChannelSegmentParams = {
        shape: endCanalShape,
        width: endCanalWidth,
        depth: endCanalDepth,
        sideSlope: endSideSlope,
        wallThickness: wallThickness,
      };

      // Usamos constante en lugar de let ya que no se reasigna
      const currentZ = transitionStart;

      // Sección antes de la transición (si hay)
      if (transitionStart > 0) {
        const beforeSegment = buildChannelSegment(
          startSegmentParams,
          transitionStart,
          canalMaterial.clone()
        );
        beforeSegment.position.z = 0;
        canalGroup.add(beforeSegment);
      }

      // Construir la transición dependiendo del tipo de canal
      if (canalShape === "rectangular" && endCanalShape === "rectangular") {
        // Usar transición suave para canales rectangulares
        const transitionGroup = buildSmoothRectangularTransition(
          startSegmentParams,
          endSegmentParams,
          transitionLength,
          canalMaterial.clone(),
          canalGroup
        );
        transitionGroup.position.z = transitionStart;
      } else {
        // Para otros tipos de canales o transiciones mixtas, usar la transición genérica
        const transitionParams: TransitionParams = {
          startParams: startSegmentParams,
          endParams: endSegmentParams,
          numSegments: TRANSITION_SEGMENTS,
          transitionLength: transitionLength,
          material: canalMaterial.clone(),
        };

        const transitionGroup = buildChannelTransition(transitionParams);
        transitionGroup.position.z = transitionStart;
        canalGroup.add(transitionGroup);
      }

      // Sección después de la transición (si hay)
      const remainingLength =
        canalLength - (transitionStart + transitionLength);
      if (remainingLength > 0) {
        const afterSegment = buildChannelSegment(
          endSegmentParams,
          remainingLength,
          canalMaterial.clone()
        );
        afterSegment.position.z = transitionStart + transitionLength;
        canalGroup.add(afterSegment);
      }
    } else {
      // Sin transición, canal uniforme
      const uniformParams: ChannelSegmentParams = {
        shape: canalShape,
        width: canalWidth,
        depth: canalDepth,
        sideSlope: sideSlope,
        wallThickness: wallThickness,
      };

      const fullSegment = buildChannelSegment(
        uniformParams,
        canalLength,
        canalMaterial.clone()
      );
      canalGroup.add(fullSegment);
    }

    // --- Crear Superficie de Agua (Water2) ---
    const existingWater = scene.getObjectByName("waterSurface");
    if (existingWater) {
      disposeScene(existingWater); // Limpiar agua anterior
    }
    if (actualWaterDepth > MIN_DEPTH_SOLVER) {
      // Calcular ancho superficial (T) en inicio y fin para el plano
      const T1 = calculateHydraulics(
        actualWaterDepth,
        canalWidth,
        sideSlope,
        canalShape
      ).T;
      let T2 = T1;
      if (enableTransition && transitionLength > 1e-6) {
        const end_h = Math.min(actualWaterDepth, endCanalDepth ?? canalDepth);
        T2 = calculateHydraulics(
          end_h,
          endCanalWidth ?? canalWidth,
          endSideSlope ?? sideSlope,
          endCanalShape ?? canalShape
        ).T;
      }
      const waterSurfWidth =
        enableTransition && transitionLength > 1e-6 ? (T1 + T2) / 2 : T1;
      const planeWidth = Math.max(waterSurfWidth, 0.01); // Ancho exacto calculado, mínimo 0.01

      // Geometría del plano para Water2
      const widthSegs = Math.max(1, Math.floor(planeWidth * 5)); // Segmentos proporcionales al ancho
      const lengthSegs = Math.max(1, Math.floor(canalLength * 5)); // Segmentos proporcionales al largo
      const waterGeometry = new THREE.PlaneGeometry(
        planeWidth,
        canalLength,
        widthSegs,
        lengthSegs
      );

      if (waterNormalMap.current) {
        // Solo crear si el normal map cargó
        // Crear material personalizado para el agua en lugar de usar Water
        // Esto nos da más control sobre la apariencia
        const waterMaterial = new THREE.MeshStandardMaterial({
          color: "#4477AA", // Color azul más oscuro para el agua
          metalness: 0.1,
          roughness: 0.2,
          normalMap: waterNormalMap.current,
          normalScale: new THREE.Vector2(0.5, 0.5), // Intensidad del normal map
          transparent: true,
          opacity: 0.9, // Ligera transparencia
          side: THREE.FrontSide,
        });

        // Crear el mesh de agua con el material personalizado
        const water = new THREE.Mesh(waterGeometry, waterMaterial);

        // Configurar el material del agua para segmentos de 2m
        if (
          water.material instanceof THREE.MeshStandardMaterial &&
          water.material.normalMap
        ) {
          // Calcular repeticiones para mantener segmentos de 2m
          const widthRepetitions = Math.max(2, Math.ceil(planeWidth / 2.0));
          const lengthRepetitions = Math.max(2, Math.ceil(canalLength / 2.0));

          // Configurar repetición de la textura
          water.material.normalMap.wrapS = water.material.normalMap.wrapT =
            THREE.RepeatWrapping;
          water.material.normalMap.repeat.set(
            widthRepetitions,
            lengthRepetitions
          );

          // Configurar otras propiedades para mejorar la apariencia
          water.material.normalMap.anisotropy =
            rendererRef.current?.capabilities.getMaxAnisotropy() ?? 4;
          water.material.envMapIntensity = 0.5; // Reducir intensidad de reflejos

          // Animar el normal map para simular movimiento del agua
          // Quitamos esta animación aquí, ya que se maneja en el bucle principal
          // para todos los tipos de agua
        }

        // Orientar el agua en la dirección correcta (como en la imagen)
        water.rotation.x = -Math.PI / 2; // Orientar horizontalmente
        water.position.y = actualWaterDepth + 0.01; // Posicionar a la altura correcta con un pequeño desplazamiento para evitar z-fighting
        water.name = "waterSurface";
        renderCanalGroup.add(water);
        waterRef.current = water; // Ahora es seguro asignar el mesh
      } else {
        console.warn(
          "Normal map del agua no disponible, no se crea la superficie."
        );
        waterRef.current = null;
      }
    } else {
      waterRef.current = null;
    } // No hay agua si el tirante es cero

    // Configurar luces (ahora depende de setupLighting)
    setupLighting(scene, renderCanalGroup);
    setSceneBuilt(true); // Marcar escena como construida
    console.log("Escena del render construida.");
  }, [
    renderState,
    texturesLoaded,
    envMapLoaded,
    selectedTexture,
    sceneBuilt,
    setupLighting,
  ]); // Dependencias clave

  // --- Efecto 4: Inicializar Entorno Three.js (Cámara, Renderer, Controles, etc.) ---
  useEffect(() => {
    // Solo ejecutar si hay div de montaje y estado válido
    if (!mountRef.current || !renderState) return;

    console.log("Inicializando entorno Three.js...");
    const mount = mountRef.current;
    let isMounted = true;

    // Escena (ya creada en Ref, el fondo/entorno se establece al cargar HDRI)
    const scene = sceneRef.current ?? new THREE.Scene(); // Usar existente o crear nueva
    sceneRef.current = scene;

    // Cámara
    const camera = new THREE.PerspectiveCamera(
      50,
      mount.clientWidth / mount.clientHeight,
      0.1,
      2000
    );
    const L = renderState.canalLength;
    const W = renderState.enableTransition
      ? (renderState.width + (renderState.endCanalWidth ?? renderState.width)) /
        2
      : renderState.width;
    const D = renderState.depth;
    const dist = Math.max(L, W, D) * 1.6; // Distancia inicial
    // Posición más parecida a la imagen de referencia (vista desde arriba y a lo largo del canal)
    camera.position.set(dist * 0.3, D * 3 + dist * 0.6, dist * 1.5); // Vista elevada a lo largo del canal
    cameraRef.current = camera;

    // Renderer
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: false,
      logarithmicDepthBuffer: false,
    }); // Logarithmic puede dar problemas con Water2/PostFX
    renderer.setSize(mount.clientWidth, mount.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 0.9;
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    rendererRef.current = renderer;
    mount.appendChild(renderer.domElement);

    // Controles
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.1;
    controls.minDistance = 1;
    controls.maxDistance = Math.max(L, W, D) * 5;
    controls.target.set(0, D / 3, 0); // Apuntar al centro del canal
    controls.update();
    controlsRef.current = controls;

    // Post-procesamiento (Bloom)
    const renderPass = new RenderPass(scene, camera);
    const bloomPass = new UnrealBloomPass(
      new THREE.Vector2(mount.clientWidth, mount.clientHeight),
      0.35,
      0.6,
      0.85
    ); // Parámetros de Bloom
    const composer = new EffectComposer(renderer);
    composer.addPass(renderPass);
    composer.addPass(bloomPass);
    composerRef.current = composer;

    // Bucle de Animación
    const clock = new THREE.Clock();
    const animate = () => {
      if (!isMounted || !composerRef.current) return; // Salir si desmontado o composer no listo
      requestRef.current = requestAnimationFrame(animate);
      const delta = clock.getDelta();
      controls.update(); // Actualizar controles (damping)

      // Animar agua si existe - compatible con ambos tipos
      const waterMesh = waterRef.current;
      if (waterMesh) {
        // Si es Water (tiene uniforms)
        if (
          "uniforms" in waterMesh.material &&
          waterMesh.material.uniforms &&
          "time" in waterMesh.material.uniforms
        ) {
          waterMesh.material.uniforms.time.value += delta * 0.3;
        }

        // Si es MeshStandardMaterial con normalMap
        if (
          waterMesh instanceof THREE.Mesh &&
          waterMesh.material instanceof THREE.MeshStandardMaterial &&
          waterMesh.material.normalMap?.offset
        ) {
          waterMesh.material.normalMap.offset.x += 0.0005;
        }
      }

      composerRef.current.render(delta); // Renderizar con post-procesado
    };
    animate(); // Iniciar bucle

    // Manejador Redimensionamiento
    const handleResize = () => {
      if (
        !isMounted ||
        !mountRef.current ||
        !cameraRef.current ||
        !rendererRef.current ||
        !composerRef.current
      )
        return;
      const w = mountRef.current.clientWidth;
      const h = mountRef.current.clientHeight;
      cameraRef.current.aspect = w / h;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(w, h);
      composerRef.current.setSize(w, h); // Actualizar tamaños
    };
    window.addEventListener("resize", handleResize);

    // Limpieza al desmontar
    return () => {
      isMounted = false;
      window.removeEventListener("resize", handleResize);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }

      // Limpiar renderer del DOM
      if (renderer.domElement.parentElement) {
        renderer.domElement.parentElement.removeChild(renderer.domElement);
      }

      // Limpiar recursos Three.js
      renderer.dispose();
      composer.dispose();
      console.log("Limpieza entorno Three.js completa.");
    };
  }, [renderState]); // Dependencia: Reiniciar todo si el estado base cambia

  // --- Efecto 5: Llamar a construir escena cuando esté listo ---
  useEffect(() => {
    // Intentar construir la escena solo si los assets están listos, el estado es válido, y no está ya construida
    if (renderState && texturesLoaded && envMapLoaded && !sceneBuilt) {
      buildRenderScene();
    }
  }, [renderState, texturesLoaded, envMapLoaded, sceneBuilt, buildRenderScene]); // Dependencias clave

  // --- Manejador Cambio de Textura (memoizado) ---
  const handleTextureChange = useCallback(
    (newTexture: TextureSet) => {
      if (newTexture !== selectedTexture) {
        console.log("Cambiando textura a:", newTexture);
        setSelectedTexture(newTexture);
        setSceneBuilt(false); // Marcar para reconstruir con la nueva textura
        // El efecto 5 se encargará de llamar a buildRenderScene
      }
    },
    [selectedTexture]
  );

  // --- Lógica de Renderizado del Componente ---

  // Componente de depuración para mostrar el tamaño de la pantalla (solo en desarrollo)
  const DebugScreenSize = () => {
    if (process.env.NODE_ENV !== "development") return null;

    return (
      <div className="fixed bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs z-50">
        Ancho: {windowWidth}px
      </div>
    );
  };

  // Note: Responsive warning removed - now supports all screen sizes

  // 1. Mostrar error si la validación del estado falló
  if (stateError) {
    return (
      /* ... JSX para mostrar error ... */
      <>
        <DebugScreenSize />
        <div className="flex items-center justify-center h-screen bg-gray-900 text-red-400 p-8 text-center">
          <div>
            <h2 className="text-2xl font-bold mb-4">Error de Configuración</h2>
            <p className="text-lg">{stateError}</p>
            <Link
              href="/dashboard/tools/canal-parametrico"
              className="mt-6 inline-block px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-lg font-medium transition-colors"
            >
              ← Volver al Editor
            </Link>
          </div>
        </div>
      </>
    );
  }

  // 2. Mostrar "Inicializando" mientras se valida el estado
  if (!renderState) {
    return (
      /* ... JSX de inicialización ... */
      <>
        <DebugScreenSize />
        <div className="absolute inset-0 bg-gray-900 flex items-center justify-center z-30">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-white text-xl mb-6">Inicializando Render...</p>
            <Link
              href="/dashboard/tools/canal-parametrico"
              className="mt-4 inline-block px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors"
            >
              ← Volver al Editor
            </Link>
          </div>
        </div>
      </>
    );
  }

  // 3. Determinar si se está cargando (assets o construcción)
  const isLoading = !texturesLoaded || !envMapLoaded || !sceneBuilt;

  // 4. Renderizar la interfaz principal
  return (
    <div className="relative w-screen h-screen bg-gray-900 overflow-hidden">
      <DebugScreenSize />
      {/* Punto de Montaje para Three.js */}
      <div ref={mountRef} className="absolute inset-0 w-full h-full" />

      {/* Overlay de Carga */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-30 backdrop-blur-sm">
          <div className="text-center p-6 bg-gray-800 rounded-lg shadow-xl">
            {/* Spinner */}
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-400 mx-auto mb-4"></div>
            {/* Mensaje */}
            <p className="text-white text-lg font-semibold mb-2">
              Cargando Render...
            </p>
            <p className="text-gray-300 text-sm">
              {!texturesLoaded ? "Cargando texturas..." : ""}
              {!envMapLoaded && texturesLoaded ? " Cargando entorno..." : ""}
              {texturesLoaded && envMapLoaded && !sceneBuilt
                ? " Construyendo escena..."
                : ""}
            </p>
            {/* Mostrar error de carga si existe */}
            {loadingError && (
              <p className="text-red-400 text-xs mt-3">Error: {loadingError}</p>
            )}
          </div>
        </div>
      )}

      {/* Controles Flotantes (Textura, Volver) - Ocultos durante la carga */}
      <div
        className={`absolute top-4 left-4 z-20 transition-opacity duration-500 ${isLoading ? "opacity-0 pointer-events-none" : "opacity-100"}`}
      >
        <div className="bg-gray-800 bg-opacity-80 backdrop-blur-sm p-3 rounded-lg shadow-lg">
          <div className="flex flex-col space-y-3">
            {/* Selector de Textura */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1.5">
                Textura Canal:
              </label>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => handleTextureChange("concrete")}
                  disabled={isLoading || selectedTexture === "concrete"}
                  className={`px-3 py-1 rounded text-sm transition-all duration-150 ${selectedTexture === "concrete" ? "bg-blue-600 text-white ring-2 ring-blue-400 shadow-md" : "bg-gray-600 hover:bg-gray-500 text-gray-200 disabled:bg-gray-700 disabled:text-gray-500 disabled:cursor-not-allowed"}`}
                >
                  Concreto
                </button>
                <button
                  type="button"
                  onClick={() => handleTextureChange("rock")}
                  disabled={isLoading || selectedTexture === "rock"}
                  className={`px-3 py-1 rounded text-sm transition-all duration-150 ${selectedTexture === "rock" ? "bg-blue-600 text-white ring-2 ring-blue-400 shadow-md" : "bg-gray-600 hover:bg-gray-500 text-gray-200 disabled:bg-gray-700 disabled:text-gray-500 disabled:cursor-not-allowed"}`}
                >
                  Roca
                </button>
              </div>
            </div>
            {/* Botón Volver */}
            <Link
              href="/dashboard/tools/canal-parametrico"
              className="mt-2 px-3 py-1.5 bg-gray-600 hover:bg-gray-500 text-white rounded-md text-sm font-medium transition-colors text-center block"
              title="Volver al editor paramétrico"
            >
              {" "}
              ← Volver al Editor{" "}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CanalParametricoRender;
