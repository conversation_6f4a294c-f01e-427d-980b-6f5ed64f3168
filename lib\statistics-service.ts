/**
 * Statistics service for tracking system resources and application usage
 */

import { invoke } from "./tauri-simple.js";

// Types for statistics data
export interface SystemResourceSnapshot {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  gpu_usage?: number;
}

export interface AppUsageEvent {
  timestamp: string;
  event_type: string;
  feature: string;
  duration?: number;
  details?: Record<string, string>;
}

export interface FeatureUsageStats {
  feature: string;
  count: number;
  total_duration: number;
  last_used: string;
}

export interface TimeSpentStats {
  section: string;
  total_duration: number;
  visit_count: number;
  last_visit: string;
}

export interface WorkflowPattern {
  steps: string[];
  count: number;
  last_used: string;
}

export interface WorkflowStats {
  patterns: WorkflowPattern[];
  most_common?: WorkflowPattern;
}

export interface SystemResources {
  cpu: {
    brand: string;
    usage: number;
    cores: number;
    frequency?: number;
  };
  memory: {
    total: number;
    used: number;
    available: number;
    usage_percent: number;
  };
  gpu?: Array<{
    name: string;
    driver_version: string;
    memory?: number;
    usage?: number;
    temperature?: number;
  }>;
}

// Time range type
export type TimeRange = "daily" | "weekly" | "monthly";

/**
 * Statistics service class
 */
class StatisticsService {
  private static instance: StatisticsService;
  private initialized: boolean = false;
  private monitoringStarted: boolean = false;
  private currentSection: string | null = null;
  private sectionStartTime: number | null = null;
  private isTauri: boolean = false;

  private constructor() {}

  /**
   * Get the singleton instance
   */
  public static getInstance(): StatisticsService {
    if (!StatisticsService.instance) {
      StatisticsService.instance = new StatisticsService();
    }
    return StatisticsService.instance;
  }

  /**
   * Initialize the statistics service
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Check if we're running in a Tauri environment
      this.isTauri = !!(
        typeof window !== "undefined" && window.__TAURI__
      );

      if (this.isTauri) {
        console.log("Initializing statistics service in Tauri environment");
        // Test if we can invoke commands
        try {
          await invoke("check_app_status");
          console.log("✅ Tauri commands are working");
        } catch (error) {
          console.warn("Some Tauri commands may not be available:", error);
        }
        this.initialized = true;
      } else {
        console.log(
          "Statistics service initialized in web environment (limited functionality)"
        );
        this.initialized = true;
      }
    } catch (error) {
      console.error("Error initializing statistics service:", error);
      this.initialized = true; // Mark as initialized even on error
    }
  }

  /**
   * Start monitoring system resources
   * @param intervalMs Interval in milliseconds between measurements
   */
  public async startSystemMonitoring(
    intervalMs: number = 60000
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (this.monitoringStarted) {
      console.log("System monitoring already started");
      return;
    }

    if (!this.isTauri) {
      console.log("System monitoring not available in web environment");
      this.startClientSideMonitoring(intervalMs);
      return;
    }

    try {
      await invoke("start_system_monitoring", { intervalMs });
      this.monitoringStarted = true;
      console.log(`System monitoring started with interval of ${intervalMs}ms`);
    } catch (error) {
      console.error("Error starting system monitoring:", error);
      // Fall back to client-side monitoring
      this.startClientSideMonitoring(intervalMs);
    }
  }

  /**
   * Start client-side monitoring (for web environment)
   * @param intervalMs Interval in milliseconds between measurements
   */
  private startClientSideMonitoring(intervalMs: number): void {
    // This is a fallback for web environment where we can't access system resources
    setInterval(() => {
      // Simulate CPU and memory usage
      const cpuUsage = 30 + Math.random() * 40;
      const memoryUsage = 40 + Math.random() * 30;

      console.log(
        `[Client-side monitoring] CPU: ${cpuUsage.toFixed(
          1
        )}%, Memory: ${memoryUsage.toFixed(1)}%`
      );
    }, intervalMs);

    this.monitoringStarted = true;
    console.log(
      `Client-side monitoring started with interval of ${intervalMs}ms`
    );
  }

  /**
   * Get current system resources
   */
  public async getCurrentSystemResources(): Promise<SystemResources> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      // Return mock data for web environment
      return {
        cpu: {
          brand: "CPU information not available",
          usage: Math.random() * 100,
          cores: navigator.hardwareConcurrency || 4,
          frequency: undefined,
        },
        memory: {
          total: 16 * 1024 * 1024 * 1024,
          used: Math.random() * 16 * 1024 * 1024 * 1024,
          available: 8 * 1024 * 1024 * 1024,
          usage_percent: Math.random() * 100,
        },
        gpu: undefined,
      };
    }

    try {
      // Try to get real system data
      const systemResources = await invoke<SystemResources>(
        "get_current_system_resources"
      );
      console.log("Real system data obtained:", systemResources);
      return systemResources;
    } catch (error) {
      console.error("Error getting current system resources:", error);
      
      // Return fallback data
      return {
        cpu: {
          brand: "Unable to detect CPU",
          usage: 0,
          cores: navigator.hardwareConcurrency || 4,
          frequency: undefined,
        },
        memory: {
          total: 0,
          used: 0,
          available: 0,
          usage_percent: 0,
        },
        gpu: undefined,
      };
    }
  }

  /**
   * Record system resources
   * @param cpuUsage CPU usage percentage
   * @param memoryUsage Memory usage percentage
   * @param gpuUsage GPU usage percentage (optional)
   */
  public async recordSystemResources(
    cpuUsage: number,
    memoryUsage: number,
    gpuUsage: number | null
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(
        `[Web mode] Would record system resources: CPU ${cpuUsage.toFixed(
          1
        )}%, Memory ${memoryUsage.toFixed(1)}%, GPU ${
          gpuUsage ? gpuUsage.toFixed(1) + "%" : "N/A"
        }`
      );
      return;
    }

    try {
      await invoke("record_system_resources", {
        cpuUsage,
        memoryUsage,
        gpuUsage,
      });
      console.log(
        `System resources recorded: CPU ${cpuUsage.toFixed(
          1
        )}%, Memory ${memoryUsage.toFixed(1)}%, GPU ${
          gpuUsage ? gpuUsage.toFixed(1) + "%" : "N/A"
        }`
      );
    } catch (error) {
      console.error("Error recording system resources:", error);
    }
  }

  /**
   * Get system resource snapshots for a specific time range
   * @param timeRange Time range ('daily', 'weekly', 'monthly')
   */
  public async getSystemResources(
    timeRange: TimeRange
  ): Promise<SystemResourceSnapshot[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(`[Web mode] Would get system resources for ${timeRange}`);
      return [];
    }

    try {
      return await invoke("get_system_resources", { timeRange });
    } catch (error) {
      console.error(`Error getting system resources for ${timeRange}:`, error);
      return [];
    }
  }

  /**
   * Track feature usage
   * @param feature Feature name
   * @param duration Duration in seconds (optional)
   */
  public async trackFeatureUsage(
    feature: string,
    duration?: number
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(
        `[Web mode] Would track feature usage: ${feature}${
          duration ? ` (${duration}s)` : ""
        }`
      );
      return;
    }

    try {
      await invoke("track_feature_usage", { feature, duration });
      console.log(
        `Tracked feature usage: ${feature}${
          duration ? ` (${duration}s)` : ""
        }`
      );
    } catch (error) {
      console.error(`Error tracking feature usage for ${feature}:`, error);
    }
  }

  /**
   * Record app usage event
   * @param eventType Event type
   * @param feature Feature name
   * @param duration Duration in seconds (optional)
   * @param details Additional details (optional)
   */
  public async recordAppUsage(
    eventType: string,
    feature: string,
    duration?: number,
    details?: Record<string, string>
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(
        `[Web mode] Would record app usage: ${eventType} - ${feature}${
          duration ? ` (${duration}s)` : ""
        }`
      );
      return;
    }

    try {
      await invoke("record_app_usage", {
        eventType,
        feature,
        duration,
        details,
      });
      console.log(
        `Recorded app usage: ${eventType} - ${feature}${
          duration ? ` (${duration}s)` : ""
        }`
      );
    } catch (error) {
      console.error(`Error recording app usage for ${feature}:`, error);
    }
  }

  /**
   * Track section visit
   * @param section Section name
   */
  public async trackSectionVisit(section: string): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    // If we're already in a section, track the exit
    if (this.currentSection && this.sectionStartTime) {
      const duration = Math.floor((Date.now() - this.sectionStartTime) / 1000);
      await this.trackSectionExit(this.currentSection, duration);

      // Also track the workflow step
      await this.trackWorkflowStep(this.currentSection, section);
    }

    // Update current section
    this.currentSection = section;
    this.sectionStartTime = Date.now();

    if (!this.isTauri) {
      console.log(`[Web mode] Would track section visit: ${section}`);
      return;
    }

    try {
      await invoke("track_section_visit", { section });
      console.log(`Tracked section visit: ${section}`);
    } catch (error) {
      console.error(`Error tracking section visit for ${section}:`, error);
    }
  }

  /**
   * Track section exit
   * @param section Section name
   * @param duration Duration in seconds
   */
  public async trackSectionExit(
    section: string,
    duration: number
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(
        `[Web mode] Would track section exit: ${section} (${duration}s)`
      );
      return;
    }

    try {
      await invoke("track_section_exit", { section, duration });
      console.log(`Tracked section exit: ${section} (${duration}s)`);
    } catch (error) {
      console.error(`Error tracking section exit for ${section}:`, error);
    }
  }

  /**
   * Track workflow step
   * @param fromSection Source section
   * @param toSection Destination section
   */
  public async trackWorkflowStep(
    fromSection: string,
    toSection: string
  ): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(
        `[Web mode] Would track workflow step: ${fromSection} → ${toSection}`
      );
      return;
    }

    try {
      await invoke("track_workflow_step", {
        fromSection,
        toSection,
      });
      console.log(`Tracked workflow step: ${fromSection} → ${toSection}`);
    } catch (error) {
      console.error(
        `Error tracking workflow step from ${fromSection} to ${toSection}:`,
        error
      );
    }
  }

  /**
   * Get feature usage statistics
   * @param timeRange Time range ('daily', 'weekly', 'monthly')
   */
  public async getFeatureUsageStats(
    timeRange: TimeRange
  ): Promise<FeatureUsageStats[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(`[Web mode] Would get feature usage stats for ${timeRange}`);
      return [];
    }

    try {
      return await invoke("get_feature_usage_stats", { timeRange });
    } catch (error) {
      console.error(
        `Error getting feature usage stats for ${timeRange}:`,
        error
      );
      return [];
    }
  }

  /**
   * Get time spent statistics
   * @param timeRange Time range ('daily', 'weekly', 'monthly')
   */
  public async getTimeSpentStats(
    timeRange: TimeRange
  ): Promise<TimeSpentStats[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(`[Web mode] Would get time spent stats for ${timeRange}`);
      return [];
    }

    try {
      return await invoke("get_time_spent_stats", { timeRange });
    } catch (error) {
      console.error(`Error getting time spent stats for ${timeRange}:`, error);
      return [];
    }
  }

  /**
   * Get workflow statistics
   * @param timeRange Time range ('daily', 'weekly', 'monthly')
   */
  public async getWorkflowStats(timeRange: TimeRange): Promise<WorkflowStats> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(`[Web mode] Would get workflow stats for ${timeRange}`);
      return { patterns: [] };
    }

    try {
      return await invoke("get_workflow_stats", { timeRange });
    } catch (error) {
      console.error(`Error getting workflow stats for ${timeRange}:`, error);
      return { patterns: [] };
    }
  }

  /**
   * Get app usage events
   * @param timeRange Time range ('daily', 'weekly', 'monthly')
   */
  public async getAppUsage(timeRange: TimeRange): Promise<AppUsageEvent[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.isTauri) {
      console.log(`[Web mode] Would get app usage events for ${timeRange}`);
      return [];
    }

    try {
      const events = await invoke<AppUsageEvent[]>("get_app_usage", {
        timeRange,
      });
      console.log(`Got ${events.length} app usage events for ${timeRange}`);
      return events;
    } catch (error) {
      console.error(`Error getting app usage for ${timeRange}:`, error);
      return [];
    }
  }
}

// Export the singleton instance
export const statisticsService = StatisticsService.getInstance();
export default statisticsService;