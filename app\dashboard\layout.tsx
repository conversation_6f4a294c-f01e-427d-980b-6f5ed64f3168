"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

import { SidebarNew } from "@/components/layout/SidebarNew";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import ClientOnly from "@/components/ClientOnly";

// Nuevos componentes para el AppBar mejorado
import { CommandPalette } from "@/components/dashboard/CommandPalette";
import { NotificationCenter } from "@/components/dashboard/NotificationCenter";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { KeyboardShortcutsHelp } from "@/components/dashboard/KeyboardShortcutsHelp";
// import { BreadcrumbNav } from "@/components/dashboard/BreadcrumbNav";

// Statistics service for tracking usage
import statisticsService from "@/lib/statistics-service";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Detectar si es móvil y establecer el estado inicial del sidebar
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // En dispositivos móviles, siempre mantenemos el sidebar cerrado
      // En escritorio, también comenzamos con el sidebar cerrado para permitir la expansión al hover
      if (mobile && isSidebarOpen) {
        setIsSidebarOpen(false);
      }
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, [isSidebarOpen]);

  // Remove artificial loading state to prevent Fast Refresh issues

  // Track section visits for statistics
  useEffect(() => {
    const trackSectionVisit = async () => {
      try {
        // Initialize statistics service if needed
        await statisticsService.initialize();

        // Extract section name from pathname
        let section = "dashboard";
        if (pathname && pathname !== "/dashboard") {
          // Get the first part after /dashboard/
          const parts = pathname.split("/");
          if (parts.length > 2) {
            section = parts[2];
          }
        }

        // Track the section visit
        await statisticsService.trackSectionVisit(section);
      } catch (error) {
        console.error("Error tracking section visit:", error);
      }
    };

    trackSectionVisit();
  }, [pathname]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Mejorar el comportamiento del hover
  const handleSidebarMouseEnter = () => {
    if (!isMobile) {
      setIsHovered(true);
    }
  };

  const handleSidebarMouseLeave = () => {
    if (!isMobile) {
      setIsHovered(false);
    }
  };

  // Remove loading screen to prevent Fast Refresh issues

  return (
    <div className="dashboard-layout flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      {/* Overlay para móvil */}
      {isMobile && isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 dark:bg-white/50"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          width: isSidebarOpen || isHovered ? "16rem" : "4.5rem",
          x: isMobile && !isSidebarOpen ? "-100%" : 0,
          boxShadow:
            (isSidebarOpen || isHovered) && !isMobile
              ? "5px 0 15px rgba(0, 0, 0, 0.1)"
              : "none",
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30,
          mass: 0.8,
          restDelta: 0.001,
          restSpeed: 0.001,
        }}
        onMouseEnter={handleSidebarMouseEnter}
        onMouseLeave={handleSidebarMouseLeave}
        className={`fixed inset-y-0 left-0 z-50 flex h-full flex-col
          bg-gray-50 dark:bg-gray-950 border-r border-gray-200 dark:border-gray-800 will-change-transform
          ${isMobile ? "w-[85%] max-w-[280px] shadow-2xl" : "lg:relative"}`}
      >
        <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200 dark:border-gray-800">
          <Link href="/dashboard" className="flex items-center gap-2 group">
            <div className="flex h-9 w-9 items-center justify-center rounded-full p-1.5 transition-all duration-300">
              <Image
                src="/pipe_logo.svg"
                alt="Pipe Logo"
                width={24}
                height={24}
                className="text-blue-500 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <AnimatePresence>
              {(isSidebarOpen || isHovered) && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30,
                    mass: 0.8,
                  }}
                  className="font-bold text-xl tracking-tight text-blue-400 whitespace-nowrap overflow-hidden flex items-center"
                >
                  <span>HYDRA</span>
                  <span className="text-blue-500 text-[0.65em] font-bold ml-0.5 mt-[-0.7em]">
                    21
                  </span>
                </motion.div>
              )}
            </AnimatePresence>
          </Link>
        </div>

        <div className="flex-1 overflow-hidden hover:overflow-y-auto px-3 py-4">
          <SidebarNew isOpen={isSidebarOpen || isHovered} />
        </div>

        <div className="border-t border-gray-200 dark:border-gray-800 p-4">
          <Link
            href="/dashboard/ayuda"
            className="flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600/20 px-3 py-2.5 text-sm font-medium text-blue-400 hover:bg-blue-600/30 transition-all duration-300 border border-blue-600/30 group"
          >
            <QuestionMarkCircleIcon className="w-5 h-5 min-w-5 transition-transform duration-300 group-hover:scale-110" />
            <AnimatePresence>
              {(isSidebarOpen || isHovered) && (
                <motion.span
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: "auto" }}
                  exit={{ opacity: 0, width: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden whitespace-nowrap"
                >
                  Obtener Ayuda
                </motion.span>
              )}
            </AnimatePresence>
          </Link>
        </div>
      </motion.aside>

      {/* Main Content */}
      <div className="dashboard-main-content flex flex-col flex-1 overflow-hidden">
        {/* Top Bar */}
        <header className="sticky top-0 z-30 flex h-16 items-center bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm transition-colors duration-300">
          <div className="flex w-full items-center justify-between px-2 sm:px-4">
            {/* Left side - Toggle button, breadcrumb and page title */}
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={toggleSidebar}
                className="inline-flex h-10 w-10 items-center justify-center rounded-md text-blue-400 hover:bg-blue-900/20 hover:text-blue-300 transition-colors"
                aria-label={isSidebarOpen ? "Cerrar menú" : "Abrir menú"}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6"
                >
                  {isSidebarOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                    />
                  )}
                </svg>
              </button>

              <div className="ml-2 hidden md:flex flex-col">
                {/* Breadcrumb Navigation */}
                {/* Page Title */}
                <h1 className="text-lg font-semibold text-blue-700 dark:text-white">
                  {!pathname || pathname === "/dashboard"
                    ? "Dashboard"
                    : pathname === "/dashboard/asistente"
                    ? "Asistente IA"
                    : pathname === "/dashboard/settings"
                    ? "Configuración"
                    : pathname === "/dashboard/profile"
                    ? "Perfil de Usuario"
                    : pathname
                        .split("/")
                        .pop()
                        ?.replace(/-/g, " ")
                        .replace(/\b\w/g, (c) => c.toUpperCase()) ||
                      "Dashboard"}
                </h1>
              </div>
            </div>

            {/* Right side - Command palette, quick actions, notifications, keyboard shortcuts, theme toggle */}
            <div className="flex items-center space-x-1 sm:space-x-2">
              {/* Dashboard Home button */}
              <Link
                href="/dashboard"
                className="inline-flex h-10 px-2 sm:px-3 items-center justify-center rounded-md text-blue-400 hover:bg-blue-900/20 hover:text-blue-300 transition-colors gap-2 border border-blue-900/30"
                aria-label="Ir al panel de control"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-5 w-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                  />
                </svg>
                <span className="text-sm font-medium hidden sm:inline">
                  Dashboard
                </span>
              </Link>

              {/* Command Palette (Search) */}
              <ClientOnly>
                <CommandPalette />
              </ClientOnly>

              {/* Quick Actions */}
              <ClientOnly>
                <QuickActions />
              </ClientOnly>

              {/* Notification Center */}
              <ClientOnly>
                <NotificationCenter />
              </ClientOnly>

              {/* Keyboard Shortcuts Help */}
              <ClientOnly>
                <KeyboardShortcutsHelp />
              </ClientOnly>

              {/* Theme Toggle */}
              <ThemeToggle />
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-3 sm:p-4 md:p-6 bg-transparent">
          <div className="mx-auto max-w-7xl h-full">
            {/* Native desktop content container with improved responsive behavior */}
            <div className="dashboard-content h-full w-full">{children}</div>
          </div>
        </main>
      </div>
    </div>
  );
}
