import "./globals.css";
import "./styles/system-settings.css"; // Import system settings styles
import type { Metadata } from "next";
import { ToastProvider } from "@/components/ui/use-toast";

import { ThemeProvider as NextThemeProvider } from "@/components/theme-provider";
import { FontProvider } from "@/contexts/FontContext";
import { TauriProvider } from "@/contexts/TauriContext";
import { SystemSettingsProvider } from "@/contexts/SystemSettingsContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import AppWrapper from "@/components/AppWrapper";
import DevTools from "@/components/developer/DevTools";

// Usamos Google Fonts a través de Head en lugar de next/font
export const metadata: Metadata = {
  title: "HYDRA\u00B2\u00B9", // Usando Unicode para superíndice 21
  description:
    "Aplicación de diseño y análisis de canales hidráulicos con asistente IA integrado.",
  icons: {
    icon: "/pipe_logo.svg",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Hack:wght@400;700&family=Rubik:wght@300;400;500;600;700&display=swap"
        />

        {/* Tauri preload script */}
        <script src="/tauri-preload.js" />
      </head>
      <body className="min-h-screen bg-background text-foreground antialiased flex flex-col">
        <script
          dangerouslySetInnerHTML={{
            __html: `
            // Polyfill for require in browser environment
            if (typeof window !== 'undefined' && typeof window.require === 'undefined') {
              window.require = function(modulePath) {
                console.warn('Browser require polyfill called for: ' + modulePath);

                // Return mock modules to prevent errors
                if (modulePath === '@tauri-apps/api') {
                  return {
                    invoke: async function(cmd, args) {
                      console.log('[Mock Tauri] invoke called: ' + cmd, args);
                      return {};
                    },
                    event: {
                      listen: async function(event, callback) {
                        console.log('[Mock Tauri] event.listen called: ' + event);
                        return {
                          unsubscribe: async function() {
                            console.log('[Mock Tauri] event unsubscribe called for: ' + event);
                          }
                        };
                      },
                      emit: async function(event, payload) {
                        console.log('[Mock Tauri] event.emit called: ' + event, payload);
                      }
                    }
                  };
                }

                // Default mock module
                return {};
              };
            }

            // Fallback script to ensure the app doesn't get stuck
            window.addEventListener('load', function() {
              setTimeout(function() {
                var appContent = document.getElementById('app-content');
                if (appContent && window.getComputedStyle(appContent).opacity === '0') {
                  appContent.style.opacity = '1';
                  appContent.style.visibility = 'visible';
                  console.log("Forced app-content visibility");
                }

                // Force show body content
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '1';
              }, 2000); // 2 second fallback (reduced from 10s)
            });
          `,
          }}
        />
        <TauriProvider>
          <NextThemeProvider>
            <FontProvider>
              <SystemSettingsProvider>
                <NotificationProvider>
                  <ToastProvider>
                    <AppWrapper>
                      <div id="app-content" className="flex-1 flex flex-col">
                        {children}
                      </div>
                      <DevTools />
                    </AppWrapper>
                  </ToastProvider>
                </NotificationProvider>
              </SystemSettingsProvider>
            </FontProvider>
          </NextThemeProvider>
        </TauriProvider>
      </body>
    </html>
  );
}
