// System resources tracking module
use serde::{Deserialize, Serialize};
use sysinfo::{System, CpuRefreshKind, RefreshKind, MemoryRefreshKind};
use tauri::{AppHandle, Runtime};
use std::sync::{Arc, Mutex};
use once_cell::sync::Lazy;

// Global system instance for better performance
static SYSTEM: Lazy<Arc<Mutex<System>>> = Lazy::new(|| {
    let mut sys = System::new_with_specifics(
        RefreshKind::new()
            .with_cpu(CpuRefreshKind::everything())
            .with_memory(MemoryRefreshKind::everything())
    );
    // Initial refresh to populate CPU info
    sys.refresh_all();
    std::thread::sleep(std::time::Duration::from_millis(200));
    sys.refresh_all();
    Arc::new(Mutex::new(sys))
});

// Structure to hold system resource information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemResources {
    pub cpu: CpuInfo,
    pub memory: MemoryInfo,
    pub gpu: Option<Vec<GpuInfo>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuInfo {
    pub brand: String,
    pub usage: f32,
    pub cores: usize,
    pub frequency: Option<u64>, // MHz
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryInfo {
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percent: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpuInfo {
    pub name: String,
    pub driver_version: String,
    pub memory: Option<u64>,
    pub usage: Option<f32>,
    pub temperature: Option<f32>,
}

// Get current system resources
#[tauri::command]
pub async fn get_current_system_resources() -> Result<SystemResources, String> {
    println!("Starting system resources collection...");

    // Get system info
    let (mut cpu_brand, cpu_cores, cpu_frequency, total_memory, used_memory, available_memory) = {
        let mut sys = SYSTEM.lock().map_err(|e| format!("Failed to lock system: {}", e))?;

        // Refresh CPU and memory data
        sys.refresh_cpu();
        sys.refresh_memory();
        std::thread::sleep(std::time::Duration::from_millis(100));
        sys.refresh_cpu();

        // Get CPU brand
        let mut cpu_brand = String::new();

        // Try global CPU info
        if !sys.global_cpu_info().brand().is_empty() {
            cpu_brand = sys.global_cpu_info().brand().to_string();
        }

        // Try first CPU
        if cpu_brand.is_empty() && !sys.cpus().is_empty() {
            let first_cpu_brand = sys.cpus()[0].brand();
            if !first_cpu_brand.is_empty() {
                cpu_brand = first_cpu_brand.to_string();
            }
        }

        let cpu_cores = sys.cpus().len();
        let cpu_frequency = sys.cpus().first().map(|cpu| cpu.frequency());
        let total_memory = sys.total_memory();
        let used_memory = sys.used_memory();
        let available_memory = sys.available_memory();

        (cpu_brand, cpu_cores, cpu_frequency, total_memory, used_memory, available_memory)
    };

    // If still no CPU brand, try Windows-specific methods
    if cpu_brand.is_empty() || cpu_brand == "Unknown" {
        #[cfg(target_os = "windows")]
        if let Some(windows_cpu) = get_cpu_info_windows() {
            cpu_brand = windows_cpu;
        } else {
            cpu_brand = format!("CPU ({} cores)", cpu_cores);
        }
        
        #[cfg(not(target_os = "windows"))]
        {
            cpu_brand = format!("CPU ({} cores)", cpu_cores);
        }
    }

    // Wait for accurate CPU usage
    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

    // Get updated CPU usage
    let cpu_usage = {
        let mut sys = SYSTEM.lock().map_err(|e| format!("Failed to lock system: {}", e))?;
        sys.refresh_cpu();
        sys.global_cpu_info().cpu_usage()
    };

    let memory_usage_percent = if total_memory > 0 {
        (used_memory as f64 / total_memory as f64) * 100.0
    } else {
        0.0
    };

    println!("CPU Info - Brand: '{}', Usage: {}%, Cores: {}, Frequency: {:?} MHz",
             cpu_brand, cpu_usage, cpu_cores, cpu_frequency);

    println!("Memory Info - Total: {} MB, Used: {} MB, Available: {} MB, Usage: {:.1}%",
             total_memory / 1024 / 1024,
             used_memory / 1024 / 1024,
             available_memory / 1024 / 1024,
             memory_usage_percent);

    // GPU information
    #[cfg(target_os = "windows")]
    let gpu_info = get_gpu_info_windows().await;
    
    #[cfg(not(target_os = "windows"))]
    let gpu_info = Vec::new();

    let result = SystemResources {
        cpu: CpuInfo {
            brand: cpu_brand,
            usage: cpu_usage,
            cores: cpu_cores,
            frequency: cpu_frequency,
        },
        memory: MemoryInfo {
            total: total_memory,
            used: used_memory,
            available: available_memory,
            usage_percent: memory_usage_percent as f32,
        },
        gpu: if gpu_info.is_empty() { None } else { Some(gpu_info) },
    };

    println!("System resources collection completed successfully");
    Ok(result)
}

// Windows-specific GPU information gathering
#[cfg(target_os = "windows")]
async fn get_gpu_info_windows() -> Vec<GpuInfo> {
    let mut gpu_info = Vec::new();
    println!("Starting GPU information collection for Windows...");

    // Try WMI first
    if let Ok(gpus) = get_gpu_info_wmi() {
        gpu_info = gpus;
    }

    // If WMI didn't work, try nvidia-smi
    if gpu_info.is_empty() {
        if let Some(nvidia_info) = get_nvidia_gpu_info().await {
            gpu_info.push(nvidia_info);
        }
    }

    // If still no GPU found, try to detect via PowerShell
    if gpu_info.is_empty() {
        if let Some(ps_gpu) = get_gpu_info_powershell().await {
            gpu_info.push(ps_gpu);
        }
    }

    // Try to get usage for detected GPUs
    for gpu in &mut gpu_info {
        if gpu.usage.is_none() {
            gpu.usage = get_gpu_usage_for_device(&gpu.name).await;
        }
    }

    println!("GPU detection completed. Found {} GPU(s)", gpu_info.len());
    gpu_info
}

// Get GPU info using WMI
#[cfg(target_os = "windows")]
fn get_gpu_info_wmi() -> Result<Vec<GpuInfo>, Box<dyn std::error::Error>> {
    use wmi::{COMLibrary, WMIConnection};
    use std::collections::HashMap;

    println!("Attempting WMI GPU detection...");

    let com_lib = COMLibrary::new()?;
    let wmi_con = WMIConnection::new(com_lib)?;

    let query = "SELECT Name, DriverVersion, AdapterRAM, Status, PNPDeviceID \
                 FROM Win32_VideoController \
                 WHERE PNPDeviceID IS NOT NULL";

    let results: Vec<HashMap<String, wmi::Variant>> = wmi_con.raw_query(query)?;

    let mut gpu_info = Vec::new();

    for gpu in results.iter() {
        let name = match gpu.get("Name") {
            Some(wmi::Variant::String(name)) => {
                // Skip virtual adapters
                if name.contains("Microsoft Basic") || 
                   name.contains("Virtual") || 
                   name.contains("Hyper-V") {
                    continue;
                }
                name.clone()
            },
            _ => continue,
        };

        let driver_version = match gpu.get("DriverVersion") {
            Some(wmi::Variant::String(version)) => version.clone(),
            _ => "Unknown".to_string(),
        };

        let adapter_ram = match gpu.get("AdapterRAM") {
            Some(wmi::Variant::UI4(ram)) => Some(*ram as u64),
            Some(wmi::Variant::UI8(ram)) => Some(*ram),
            Some(wmi::Variant::I4(ram)) if *ram > 0 => Some(*ram as u64),
            Some(wmi::Variant::I8(ram)) if *ram > 0 => Some(*ram as u64),
            _ => None,
        };

        println!("Found GPU: {} - Driver: {}", name, driver_version);

        gpu_info.push(GpuInfo {
            name,
            driver_version,
            memory: adapter_ram,
            usage: None,
            temperature: None,
        });
    }

    Ok(gpu_info)
}

// Get GPU info using PowerShell
#[cfg(target_os = "windows")]
async fn get_gpu_info_powershell() -> Option<GpuInfo> {
    let script = r#"
        $gpu = Get-WmiObject Win32_VideoController | Where-Object {$_.Status -eq 'OK' -and $_.Name -notlike '*Microsoft*' -and $_.Name -notlike '*Virtual*'} | Select-Object -First 1
        if ($gpu) {
            $name = $gpu.Name
            $driver = $gpu.DriverVersion
            $memory = $gpu.AdapterRAM
            "$name|$driver|$memory"
        }
    "#;

    let output = tokio::process::Command::new("powershell")
        .args(&["-NoProfile", "-Command", script])
        .output()
        .await
        .ok()?;

    if output.status.success() {
        let output_str = String::from_utf8_lossy(&output.stdout);
        let parts: Vec<&str> = output_str.trim().split('|').collect();
        
        if parts.len() >= 3 {
            return Some(GpuInfo {
                name: parts[0].to_string(),
                driver_version: parts[1].to_string(),
                memory: parts[2].parse::<u64>().ok(),
                usage: None,
                temperature: None,
            });
        }
    }

    None
}

// Start monitoring system resources at regular intervals
#[tauri::command]
pub async fn start_system_monitoring<R: Runtime>(app_handle: AppHandle<R>, interval_ms: u64) -> Result<(), String> {
    let app_handle_clone = app_handle.clone();

    tauri::async_runtime::spawn(async move {
        loop {
            match get_current_system_resources().await {
                Ok(resources) => {
                    let _ = super::record_system_resources(
                        app_handle_clone.clone(),
                        resources.cpu.usage,
                        resources.memory.usage_percent,
                        resources.gpu.as_ref().and_then(|gpus| gpus.first().and_then(|gpu| gpu.usage)),
                    ).await;
                },
                Err(e) => {
                    println!("Failed to get system resources: {}", e);
                }
            }

            tokio::time::sleep(tokio::time::Duration::from_millis(interval_ms)).await;
        }
    });

    Ok(())
}

// Get GPU usage for a specific device
#[cfg(target_os = "windows")]
async fn get_gpu_usage_for_device(gpu_name: &str) -> Option<f32> {
    println!("Getting usage for GPU: {}", gpu_name);

    // Try NVIDIA-specific methods
    if gpu_name.to_lowercase().contains("nvidia") || gpu_name.to_lowercase().contains("geforce") {
        if let Some(usage) = get_nvidia_gpu_usage().await {
            return Some(usage);
        }
    }

    // Try generic Windows performance counters
    get_gpu_usage_performance_counters().await
}

// Get NVIDIA GPU information
#[cfg(target_os = "windows")]
async fn get_nvidia_gpu_info() -> Option<GpuInfo> {
    let output = tokio::process::Command::new("nvidia-smi")
        .args(&["--query-gpu=name,driver_version,memory.total,utilization.gpu,temperature.gpu",
                "--format=csv,noheader,nounits"])
        .output()
        .await
        .ok()?;

    if !output.status.success() {
        return None;
    }

    let output_str = String::from_utf8_lossy(&output.stdout);
    let parts: Vec<&str> = output_str.trim().split(", ").collect();

    if parts.len() >= 5 {
        return Some(GpuInfo {
            name: parts[0].to_string(),
            driver_version: parts[1].to_string(),
            memory: parts[2].parse::<u64>().ok().map(|m| m * 1024 * 1024),
            usage: parts[3].parse::<f32>().ok(),
            temperature: parts[4].parse::<f32>().ok(),
        });
    }

    None
}

// Get NVIDIA GPU usage
#[cfg(target_os = "windows")]
async fn get_nvidia_gpu_usage() -> Option<f32> {
    let output = tokio::process::Command::new("nvidia-smi")
        .args(&["--query-gpu=utilization.gpu", "--format=csv,noheader,nounits"])
        .output()
        .await
        .ok()?;

    if output.status.success() {
        let output_str = String::from_utf8_lossy(&output.stdout);
        return output_str.trim().parse::<f32>().ok();
    }

    None
}

// Get GPU usage via Windows Performance Counters
#[cfg(target_os = "windows")]
async fn get_gpu_usage_performance_counters() -> Option<f32> {
    let script = r#"
        try {
            $gpu = (Get-Counter '\GPU Engine(*)\Utilization Percentage' -ErrorAction Stop).CounterSamples | 
                Where-Object {$_.InstanceName -like '*engtype_3D*'} | 
                Measure-Object -Property CookedValue -Average
            if ($gpu.Average) { [math]::Round($gpu.Average, 2) } else { 0 }
        } catch { 0 }
    "#;

    let output = tokio::process::Command::new("powershell")
        .args(&["-NoProfile", "-Command", script])
        .output()
        .await
        .ok()?;

    if output.status.success() {
        let output_str = String::from_utf8_lossy(&output.stdout);
        if let Ok(usage) = output_str.trim().parse::<f32>() {
            if usage > 0.0 {
                return Some(usage);
            }
        }
    }

    None
}

// Get CPU info using Windows methods
#[cfg(target_os = "windows")]
fn get_cpu_info_windows() -> Option<String> {
    use wmi::{COMLibrary, WMIConnection};
    use std::collections::HashMap;

    println!("Attempting to get CPU info via WMI...");

    if let Ok(com_lib) = COMLibrary::new() {
        if let Ok(wmi_con) = WMIConnection::new(com_lib) {
            let query = "SELECT Name FROM Win32_Processor";
            if let Ok(results) = wmi_con.raw_query::<HashMap<String, wmi::Variant>>(query) {
                for cpu in results {
                    if let Some(wmi::Variant::String(name)) = cpu.get("Name") {
                        return Some(name.trim().to_string());
                    }
                }
            }
        }
    }

    // Try PowerShell as fallback
    if let Ok(output) = std::process::Command::new("powershell")
        .args(&["-Command", "Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty Name"])
        .output()
    {
        if output.status.success() {
            let cpu_name = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !cpu_name.is_empty() {
                return Some(cpu_name);
            }
        }
    }

    None
}

#[cfg(not(target_os = "windows"))]
fn get_cpu_info_windows() -> Option<String> {
    None
}