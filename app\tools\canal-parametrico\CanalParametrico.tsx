"use client";

import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
// Eliminamos las importaciones relacionadas con la navegación que no usaremos
import ErrorBoundary from "../../../components/ErrorBoundary";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { OBJExporter } from "three/examples/jsm/exporters/OBJExporter";
import { STLExporter } from "three/examples/jsm/exporters/STLExporter";
import { GLTFExporter } from "three/examples/jsm/exporters/GLTFExporter";

// Importamos un hook personalizado para el navegador del cliente
const useIsClient = () => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  return isClient;
};

// Importar el componente HomeButton
import HomeButton from "./HomeButton";

// Importar componentes refactorizados
import { EnergyProfileData } from "./EnergyProfileChart";
import CollapsibleControlPanel from "./CollapsibleControlPanel";
import ViewerControls from "./ViewerControls";
import ExportMenu from "./ExportMenu";
// import EnergyProfileModal from "./EnergyProfileModal"; // Eliminamos esta importación
// import NewHydraulicDetailedModal from "./NewHydraulicDetailedModal"; // Comentamos esta línea
import EngineeringDetailedModal from "./EngineeringDetailedModal"; // Importamos el modal de ingeniería
import EnhancedResultsDisplay from "./EnhancedResultsDisplay";
import ErrorDisplay from "./ErrorDisplay";
import PdfExporter from "./PdfExporter";
import DimensionAnnotations from "./DimensionAnnotations";
import CaptureImageButton from "./CaptureImageButton";
import { registerReport } from "@/lib/reportHelper";
import ViewerFloatingPanel from "./ViewerFloatingPanel";
import SideControlButtons from "./SideControlButtons";
import { Button } from "@/components/ui/button";
import {
  LayoutGrid,
  AlignRight,
  Ruler,
  RotateCw,
  Sun,
  Moon,
  ListChecks,
} from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";

// Importar componentes Select en la parte superior del archivo
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Importar utilidad para combinación de clases
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

// Import jsPDF al inicio del archivo
import { jsPDF } from "jspdf";

// --- Constantes ---
const MAX_ITER_SOLVER = 100;
const TOLERANCE_SOLVER = 1e-6;
const INITIAL_DEPTH_GUESS = 0.1;
const MIN_DEPTH_SOLVER = 1e-5;
const GRAVITY = 9.81;

// --- Tipos Exportables ---
export type CanalShape = "rectangular" | "trapezoidal" | "triangular";
export type InputMode = "tirante" | "caudal";
export interface ChannelSegmentParams {
  shape: CanalShape;
  width: number;
  depth: number;
  sideSlope: number;
  wallThickness: number;
}

// --- Tipos Internos ---
export interface HydraulicResults {
  A: number;
  P: number;
  R: number;
  T: number;
  V: number;
  Q: number;
  y: number;
  yc?: number;
  E?: number;
  Fr?: number;
}

// --- Funciones Utilitarias (Se mantienen igual) ---
function disposeScene(obj: THREE.Object3D | null) {
  if (!obj) return;

  // Crear una copia del array de hijos para evitar problemas al modificar durante la iteración
  const children = [...obj.children];

  // Procesar todos los hijos recursivamente
  for (const child of children) {
    disposeScene(child);
  }

  // Limpiar el objeto actual
  if (obj instanceof THREE.Mesh) {
    if (obj.geometry) {
      obj.geometry.dispose();
    }

    if (obj.material) {
      // Material puede ser un array o un material individual
      if (Array.isArray(obj.material)) {
        obj.material.forEach((m) => {
          if (m instanceof THREE.MeshStandardMaterial) {
            m.map?.dispose();
            m.normalMap?.dispose();
            m.roughnessMap?.dispose();
            m.aoMap?.dispose();
            m.metalnessMap?.dispose();
            m.emissiveMap?.dispose();
          }
          m.dispose();
        });
      } else {
        const mat = obj.material;
        if (mat instanceof THREE.MeshStandardMaterial) {
          mat.map?.dispose();
          mat.normalMap?.dispose();
          mat.roughnessMap?.dispose();
          mat.aoMap?.dispose();
          mat.metalnessMap?.dispose();
          mat.emissiveMap?.dispose();
        }
        mat.dispose();
      }
    }
  } else if (obj instanceof THREE.Line) {
    if (obj.geometry) {
      obj.geometry.dispose();
    }
    if (Array.isArray(obj.material)) {
      obj.material.forEach((m) => m.dispose());
    } else if (obj.material) {
      obj.material.dispose();
    }
  } else if (obj instanceof THREE.Light) {
    if ((obj as THREE.DirectionalLight).shadow?.map) {
      (obj as THREE.DirectionalLight).shadow.map?.dispose();
    }
  }

  // Eliminar el objeto de su padre si tiene uno
  if (obj.parent) {
    obj.parent.remove(obj);
  }
}
function calculateHydraulics(
  h: number,
  B: number,
  m: number,
  S0: number,
  n: number,
  shape: CanalShape
): HydraulicResults {
  /* ... (código de calculateHydraulics) ... */ let A = 0,
    P = 0,
    T = 0;
  h = Math.max(h, 0);
  if (shape === "rectangular") {
    B = Math.max(B, 0);
    m = 0;
    A = B * h;
    P = B + 2 * h;
    T = B;
  } else if (shape === "trapezoidal") {
    B = Math.max(B, 0);
    m = Math.max(m, 0);
    T = B + 2 * m * h;
    A = (B + T) * h * 0.5;
    P = B + 2 * h * Math.sqrt(1 + m * m);
  } else {
    m = Math.max(m, 1e-6);
    B = 0;
    T = 2 * m * h;
    A = m * h * h;
    P = 2 * h * Math.sqrt(1 + m * m);
  }
  const R = P > 1e-9 ? A / P : 0;
  const V =
    P > 1e-9 && S0 >= 0 && n > 1e-9
      ? (1 / n) * Math.pow(R, 2 / 3) * Math.sqrt(S0)
      : 0;
  const Q = V * A;
  const D = T > 1e-9 ? A / T : 0;
  const Fr =
    V > 1e-9 && D > 1e-9 && GRAVITY > 1e-9 ? V / Math.sqrt(GRAVITY * D) : 0;
  const E = h + (V * V) / (2 * GRAVITY);
  return { A, P, R, T, V, Q, y: h, E, Fr };
}
function solveNormalDepth(
  Qtarget: number,
  B: number,
  m: number,
  S0: number,
  n: number,
  shape: CanalShape
): number {
  /* ... (código de solveNormalDepth) ... */ if (
    Qtarget <= 0 ||
    S0 <= 0 ||
    n <= 0
  )
    return 0;
  if (shape === "rectangular") {
    B = Math.max(B, 1e-6);
    m = 0;
  } else if (shape === "trapezoidal") {
    B = Math.max(B, 0);
    m = Math.max(m, 0);
    if (B < 1e-9 && m < 1e-9) return 0;
  } else {
    B = 0;
    m = Math.max(m, 1e-6);
  }
  if (S0 < 1e-9 || n < 1e-9) return 0;
  let y = INITIAL_DEPTH_GUESS;
  try {
    if (shape === "rectangular" && B > 1e-6) {
      y = Math.pow((Qtarget * n) / (Math.sqrt(S0) * B), 3 / 5);
    } else if (shape === "triangular" && m > 1e-6) {
      y = Math.pow(
        (2 * Math.sqrt(1 + m * m) * Qtarget * n) /
          (Math.sqrt(S0) * Math.pow(m, 5 / 3)),
        3 / 8
      );
    } else if (shape === "trapezoidal") {
      if (B > m * INITIAL_DEPTH_GUESS * 5 && B > 1e-6) {
        y = Math.pow((Qtarget * n) / (Math.sqrt(S0) * B), 3 / 5);
      } else if (m > 1e-6) {
        y = Math.pow(
          (2 * Math.sqrt(1 + m * m) * Qtarget * n) /
            (Math.sqrt(S0) * Math.pow(m, 5 / 3)),
          3 / 8
        );
      }
    }
    y = Math.max(y, INITIAL_DEPTH_GUESS);
  } catch {
    y = INITIAL_DEPTH_GUESS;
  }
  y = Math.max(y, MIN_DEPTH_SOLVER);
  for (let i = 0; i < MAX_ITER_SOLVER; i++) {
    const h = calculateHydraulics(y, B, m, S0, n, shape);
    const f = h.Q - Qtarget;
    if (Math.abs(f) < TOLERANCE_SOLVER * Math.max(Qtarget, 1e-3))
      return Math.max(y, 0);
    let dPdy: number;
    if (shape === "rectangular") dPdy = 2;
    else dPdy = 2 * Math.sqrt(1 + m * m);
    const dAdy = h.T;
    let f_prime = 0;
    if (h.P > 1e-9 && n > 1e-9 && h.A > 1e-9) {
      const K = Math.sqrt(S0) / n;
      const A_pow_2_3 = Math.pow(h.A, 2.0 / 3.0);
      const P_pow_neg_2_3 = Math.pow(h.P, -2.0 / 3.0);
      const A_pow_5_3 = h.A * A_pow_2_3;
      const P_pow_neg_5_3 = P_pow_neg_2_3 / h.P;
      f_prime =
        K *
        ((5.0 / 3.0) * A_pow_2_3 * dAdy * P_pow_neg_2_3 -
          (2.0 / 3.0) * A_pow_5_3 * P_pow_neg_5_3 * dPdy);
    }
    if (Math.abs(f_prime) < 1e-6) {
      y += (f > 0 ? -1 : 1) * y * 0.05 + MIN_DEPTH_SOLVER * (f > 0 ? -1 : 1);
      y = Math.max(y, MIN_DEPTH_SOLVER);
      if (i > MAX_ITER_SOLVER / 2 && Math.abs(f) > TOLERANCE_SOLVER * 10) {
        console.warn("Solver stuck (normal depth, zero derivative).");
        return Math.max(y, 0);
      }
      continue;
    }
    const delta_y = f / f_prime;
    y -= delta_y;
    y = Math.max(y, MIN_DEPTH_SOLVER);
  }
  console.warn(
    `Normal depth solver did not converge for Q=${Qtarget}. Retornando mejor estimación:`,
    y
  );
  return Math.max(y, 0);
}
function solveCriticalDepth(
  Qtarget: number,
  B: number,
  m: number,
  shape: CanalShape
): number {
  /* ... (código de solveCriticalDepth) ... */ if (Qtarget <= 0) return 0;
  if (shape === "rectangular") {
    B = Math.max(B, 1e-6);
    m = 0;
  } else if (shape === "trapezoidal") {
    B = Math.max(B, 0);
    m = Math.max(m, 0);
    if (B < 1e-9 && m < 1e-9) return 0;
  } else {
    B = 0;
    m = Math.max(m, 1e-6);
  }
  const Q2_g = (Qtarget * Qtarget) / GRAVITY;
  let yc = INITIAL_DEPTH_GUESS;
  try {
    if (shape === "rectangular" && B > 1e-6) {
      yc = Math.pow(Q2_g / (B * B), 1 / 3);
    } else if (shape === "triangular" && m > 1e-6) {
      yc = Math.pow((2 * Q2_g) / (m * m), 1 / 5);
    } else if (shape === "trapezoidal") {
      if (B > m * yc * 5 && B > 1e-6) {
        yc = Math.pow(Q2_g / (B * B), 1 / 3);
      } else if (m > 1e-6) {
        yc = Math.pow((2 * Q2_g) / (m * m), 1 / 5);
      }
    }
    yc = Math.max(yc, INITIAL_DEPTH_GUESS);
  } catch {
    yc = INITIAL_DEPTH_GUESS;
  }
  yc = Math.max(yc, MIN_DEPTH_SOLVER);
  for (let i = 0; i < MAX_ITER_SOLVER; i++) {
    let A = 0,
      T = 0;
    if (shape === "rectangular") {
      A = B * yc;
      T = B;
    } else if (shape === "trapezoidal") {
      T = B + 2 * m * yc;
      A = (B + T) * yc * 0.5;
    } else {
      T = 2 * m * yc;
      A = m * yc * yc;
    }
    if (T <= 1e-9 || A <= 1e-9) {
      if (i == 0) yc = INITIAL_DEPTH_GUESS * 1.5;
      else yc *= 1.1;
      yc = Math.max(yc, MIN_DEPTH_SOLVER);
      continue;
    }
    const f = (A * A * A) / T - Q2_g;
    if (Math.abs(f) < TOLERANCE_SOLVER * Math.max(Q2_g, 1e-6))
      return Math.max(yc, 0);
    let dAdy = 0,
      dTdy = 0;
    if (shape === "rectangular") {
      dAdy = B;
      dTdy = 0;
    } else if (shape === "trapezoidal") {
      dAdy = B + 2 * m * yc;
      dTdy = 2 * m;
    } else {
      dAdy = 2 * m * yc;
      dTdy = 2 * m;
    }
    let f_prime = 0;
    if (T > 1e-9) {
      f_prime = (3 * A * A * T * dAdy - A * A * A * dTdy) / (T * T);
    }
    if (Math.abs(f_prime) < 1e-6) {
      yc += (f > 0 ? -1 : 1) * yc * 0.05 + MIN_DEPTH_SOLVER * (f > 0 ? -1 : 1);
      yc = Math.max(yc, MIN_DEPTH_SOLVER);
      if (i > MAX_ITER_SOLVER / 2 && Math.abs(f) > TOLERANCE_SOLVER * 10) {
        console.warn("Solver stuck (critical depth, zero derivative).");
        return Math.max(yc, 0);
      }
      continue;
    }
    const delta_y = f / f_prime;
    yc -= delta_y;
    yc = Math.max(yc, MIN_DEPTH_SOLVER);
  }
  console.warn(
    `Critical depth solver did not converge for Q=${Qtarget}. Retornando mejor estimación:`,
    yc
  );
  return Math.max(yc, 0);
}

// --- Constantes ---
const TABLET_BREAKPOINT = 1024; // Usamos 1024px como límite para asegurar que funcione correctamente

// --- Componente Principal ---
const CanalParametrico: React.FC = () => {
  // --- Refs ---
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const canalGroupRef = useRef<THREE.Group | null>(null);
  const requestRef = useRef<number>();
  const exportButtonRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  // --- Estado para detección de tamaño de pantalla ---
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== "undefined" ? window.innerWidth : 0
  );

  // Efecto para detectar cambios en el tamaño de la ventana
  useEffect(() => {
    // Función para actualizar el estado con el ancho actual
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Verificar el ancho inicial
    handleResize();

    // Agregar listener para cambios de tamaño
    window.addEventListener("resize", handleResize);

    // Limpiar listener al desmontar
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // --- Estado ---
  // Geometría Inicial
  const [canalShape, setCanalShape] = useState<CanalShape>("rectangular");
  const [canalWidth, setCanalWidth] = useState<number>(5);
  const [canalDepth, setCanalDepth] = useState<number>(3);
  const [sideSlope, setSideSlope] = useState<number>(1.5);
  const [wallThickness, setWallThickness] = useState<number>(0.3);
  // Geometría General y Flujo
  const [canalLength, setCanalLength] = useState<number>(30);
  const [canalSlope, setCanalSlope] = useState<number>(0.005);
  const [roughness, setRoughness] = useState<number>(0.015);
  // Entrada de Flujo
  const [inputMode, setInputMode] = useState<InputMode>("tirante");
  const [waterDepthInput, setWaterDepthInput] = useState<number>(1.5);
  const [dischargeInput, setDischargeInput] = useState<number>(10);
  // Transición
  const [enableTransition, setEnableTransition] = useState<boolean>(false);
  const [transitionStart, setTransitionStart] = useState<number>(10);
  const [transitionLength, setTransitionLength] = useState<number>(10);
  const [endCanalShape, setEndCanalShape] = useState<CanalShape>("trapezoidal");
  const [endCanalWidth, setEndCanalWidth] = useState<number>(3);
  const [endCanalDepth, setEndCanalDepth] = useState<number>(3);
  const [endSideSlope, setEndSideSlope] = useState<number>(1.0);
  // UI y Resultados Calculados
  const [result, setResult] = useState<HydraulicResults | null>(null);
  const [actualWaterDepth, setActualWaterDepth] =
    useState<number>(waterDepthInput);
  const [showGrid, setShowGrid] = useState<boolean>(true);
  const [showAxes, setShowAxes] = useState<boolean>(true);
  const [showDimensions, setShowDimensions] = useState<boolean>(true); // New state for dimension annotations
  const [isDarkMode, setIsDarkMode] = useState<boolean>(true);
  const [exporting, setExporting] = useState<boolean>(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showHydraulicDetailedModal, setShowHydraulicDetailedModal] =
    useState(false);
  const [error, setError] = useState<Error | null>(null);

  // --- Toast hook ---
  const { toast } = useToast();

  // --- Efecto para Cálculos Hidráulicos ---
  useEffect(() => {
    // ... (código de cálculos hidráulicos se mantiene igual) ...
    let calculatedDepth: number;
    const B =
      canalShape === "rectangular" || canalShape === "trapezoidal"
        ? canalWidth
        : 0;
    const m = canalShape === "rectangular" ? 0 : Math.max(sideSlope, 0.01);
    const currentCanalDepthH = canalDepth;
    if (inputMode === "caudal") {
      calculatedDepth = solveNormalDepth(
        dischargeInput,
        B,
        m,
        canalSlope,
        roughness,
        canalShape
      );
      if (calculatedDepth > currentCanalDepthH + TOLERANCE_SOLVER) {
        /* console.warn(...) */
      }
    } else {
      calculatedDepth = Math.min(
        Math.max(waterDepthInput, 0),
        currentCanalDepthH
      );
      if (waterDepthInput > currentCanalDepthH) {
        setWaterDepthInput(currentCanalDepthH);
      } else if (waterDepthInput < 0) {
        setWaterDepthInput(0);
      }
    }
    const hydraulics = calculateHydraulics(
      calculatedDepth,
      B,
      m,
      canalSlope,
      roughness,
      canalShape
    );
    const Q_for_critical =
      inputMode === "caudal" ? dischargeInput : hydraulics.Q;
    const yc =
      Q_for_critical > MIN_DEPTH_SOLVER * 0.1
        ? solveCriticalDepth(Q_for_critical, B, m, canalShape)
        : 0;
    hydraulics.yc = yc;
    setActualWaterDepth(calculatedDepth);
    setResult(hydraulics);
  }, [
    inputMode,
    waterDepthInput,
    dischargeInput,
    canalWidth,
    canalDepth,
    canalSlope,
    sideSlope,
    roughness,
    canalShape,
  ]);

  // --- Helper Geometría: Segmento del Canal (Sólido) ---
  const buildChannelSegment = useCallback(
    (
      params: ChannelSegmentParams,
      length: number,
      material: THREE.MeshStandardMaterial
    ): THREE.Group => {
      /* ... (código de buildChannelSegment sin cambios) ... */ const segmentGroup =
        new THREE.Group();
      if (length <= 1e-6) return segmentGroup;
      const {
        shape,
        width,
        depth,
        sideSlope: m_param,
        wallThickness: wt,
      } = params;
      const m = shape === "rectangular" ? 0 : Math.max(m_param, 0.01);
      const B =
        shape === "rectangular" || shape === "trapezoidal"
          ? Math.max(width, 0)
          : 0;
      const H = Math.max(depth, 0.1);
      const WT = Math.max(wt, 0.05);
      const extrudeSettings = { depth: length, bevelEnabled: false };
      const halfLength = length / 2;
      if (shape === "rectangular") {
        const baseGeo = new THREE.BoxGeometry(B + 2 * WT, WT, length);
        const baseMesh = new THREE.Mesh(baseGeo, material.clone());
        baseMesh.position.set(0, -WT / 2, 0);
        baseMesh.name = "base";
        segmentGroup.add(baseMesh);
        const wallGeo = new THREE.BoxGeometry(WT, H, length);
        const leftWall = new THREE.Mesh(wallGeo.clone(), material.clone());
        leftWall.position.set(-(B / 2) - WT / 2, H / 2, 0);
        leftWall.name = "leftWall";
        segmentGroup.add(leftWall);
        const rightWall = new THREE.Mesh(wallGeo, material.clone());
        rightWall.position.set(B / 2 + WT / 2, H / 2, 0);
        rightWall.name = "rightWall";
        segmentGroup.add(rightWall);
      } else {
        const i_bl = new THREE.Vector2(-B / 2, 0);
        const i_tl = new THREE.Vector2(-B / 2 - m * H, H);
        const i_br = new THREE.Vector2(B / 2, 0);
        const i_tr = new THREE.Vector2(B / 2 + m * H, H);
        const normFactor = Math.sqrt(1 + m * m);
        const perpOutL = new THREE.Vector2(
          1 / normFactor,
          m / normFactor
        ).multiplyScalar(WT);
        const perpOutR = new THREE.Vector2(
          -1 / normFactor,
          m / normFactor
        ).multiplyScalar(WT);
        const o_tl = i_tl.clone().add(perpOutL);
        const o_tr = i_tr.clone().add(perpOutR);
        const o_bl = i_bl.clone().add(perpOutL);
        const o_br = i_br.clone().add(perpOutR);
        if (shape === "trapezoidal" && B > 1e-6) {
          const baseShape = new THREE.Shape();
          baseShape.moveTo(o_bl.x, o_bl.y);
          baseShape.lineTo(o_br.x, o_br.y);
          baseShape.lineTo(i_br.x, i_br.y);
          baseShape.lineTo(i_bl.x, i_bl.y);
          baseShape.closePath();
          const baseGeo = new THREE.ExtrudeGeometry(baseShape, extrudeSettings);
          const baseMesh = new THREE.Mesh(baseGeo, material.clone());
          baseMesh.name = "base";
          segmentGroup.add(baseMesh);
        }
        const leftWallShape = new THREE.Shape();
        leftWallShape.moveTo(i_bl.x, i_bl.y);
        leftWallShape.lineTo(i_tl.x, i_tl.y);
        leftWallShape.lineTo(o_tl.x, o_tl.y);
        leftWallShape.lineTo(o_bl.x, o_bl.y);
        leftWallShape.closePath();
        const leftWallGeo = new THREE.ExtrudeGeometry(
          leftWallShape,
          extrudeSettings
        );
        const leftWallMesh = new THREE.Mesh(leftWallGeo, material.clone());
        leftWallMesh.name = "leftWall";
        segmentGroup.add(leftWallMesh);
        const rightWallShape = new THREE.Shape();
        rightWallShape.moveTo(i_br.x, i_br.y);
        rightWallShape.lineTo(i_tr.x, i_tr.y);
        rightWallShape.lineTo(o_tr.x, o_tr.y);
        rightWallShape.lineTo(o_br.x, o_br.y);
        rightWallShape.closePath();
        const rightWallGeo = new THREE.ExtrudeGeometry(
          rightWallShape,
          extrudeSettings
        );
        const rightWallMesh = new THREE.Mesh(rightWallGeo, material.clone());
        rightWallMesh.name = "rightWall";
        segmentGroup.add(rightWallMesh);
        segmentGroup.children.forEach((child) => {
          if (
            child instanceof THREE.Mesh &&
            child.geometry instanceof THREE.ExtrudeGeometry
          ) {
            child.position.z -= halfLength;
          }
        });
      }
      segmentGroup.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
          child.position.z += halfLength;
        }
      });
      return segmentGroup;
    },
    []
  );

  // --- Helper Geometría: Segmento de Agua ---
  const buildWaterSegment = useCallback(
    (
      params: ChannelSegmentParams,
      waterDepth: number,
      length: number,
      material: THREE.Material
    ): THREE.Mesh | null => {
      // Validaciones iniciales
      if (
        length <= 1e-6 ||
        waterDepth <= MIN_DEPTH_SOLVER ||
        waterDepth > params.depth + TOLERANCE_SOLVER
      ) {
        return null;
      }

      const { shape, width, sideSlope: m_param, depth: channelDepth } = params;
      const actualRenderWaterDepth = Math.min(waterDepth, channelDepth);
      const m = shape === "rectangular" ? 0 : Math.max(m_param, 0.01);
      const B =
        shape === "rectangular" || shape === "trapezoidal"
          ? Math.max(width, 0)
          : 0;
      const halfLength = length / 2;
      let waterGeometry: THREE.BufferGeometry;

      // Crear geometría según la forma del canal
      if (shape === "rectangular") {
        // Para canales rectangulares, usamos ExtrudeGeometry igual que para otros tipos
        // para mantener consistencia en la orientación
        const waterShape = new THREE.Shape();

        // Reducir ligeramente el ancho para evitar z-fighting
        const waterWidth = Math.max(B - 0.01, B * 0.999);

        // Definir los vértices de la sección transversal del agua rectangular
        const w_bl = new THREE.Vector2(-waterWidth / 2, 0); // Esquina inferior izquierda
        const w_br = new THREE.Vector2(waterWidth / 2, 0); // Esquina inferior derecha
        const w_tl = new THREE.Vector2(-waterWidth / 2, actualRenderWaterDepth); // Esquina superior izquierda
        const w_tr = new THREE.Vector2(waterWidth / 2, actualRenderWaterDepth); // Esquina superior derecha

        // Crear la forma 2D
        waterShape.moveTo(w_bl.x, w_bl.y);
        waterShape.lineTo(w_tl.x, w_tl.y);
        waterShape.lineTo(w_tr.x, w_tr.y);
        waterShape.lineTo(w_br.x, w_br.y);
        waterShape.closePath();

        // Extruir la forma para crear el volumen 3D
        const extrudeSettings = { depth: length, bevelEnabled: false };
        try {
          waterGeometry = new THREE.ExtrudeGeometry(
            waterShape,
            extrudeSettings
          );
        } catch (e) {
          console.error(
            "Error creando geometría de agua rectangular (extrude):",
            e
          );
          return null;
        }
      } else {
        // Para canales trapezoidales y triangulares, crear forma personalizada
        const waterShape = new THREE.Shape();
        const T_water = B + 2 * m * actualRenderWaterDepth; // Ancho superior del agua

        // Definir los vértices de la sección transversal del agua
        const w_bl = new THREE.Vector2(-B / 2, 0); // Esquina inferior izquierda
        const w_br = new THREE.Vector2(B / 2, 0); // Esquina inferior derecha
        const w_tl = new THREE.Vector2(-T_water / 2, actualRenderWaterDepth); // Esquina superior izquierda
        const w_tr = new THREE.Vector2(T_water / 2, actualRenderWaterDepth); // Esquina superior derecha

        // Crear la forma 2D
        waterShape.moveTo(w_bl.x, w_bl.y);
        waterShape.lineTo(w_tl.x, w_tl.y);
        waterShape.lineTo(w_tr.x, w_tr.y);
        waterShape.lineTo(w_br.x, w_br.y);
        waterShape.closePath();

        // Extruir la forma para crear el volumen 3D
        const extrudeSettings = { depth: length, bevelEnabled: false };
        try {
          waterGeometry = new THREE.ExtrudeGeometry(
            waterShape,
            extrudeSettings
          );
        } catch (e) {
          console.error("Error creando geometría de agua (extrude):", e);
          return null;
        }
      }

      // Crear el mesh con la geometría y material
      const waterMesh = new THREE.Mesh(waterGeometry, material);
      waterMesh.name = "waterMesh";
      waterMesh.castShadow = false;
      waterMesh.receiveShadow = true;

      // Posicionar correctamente el agua para todos los tipos de canal
      // Ajustar posición Z por la extrusión (para todos los tipos)
      waterMesh.position.z = -halfLength;

      // Elevar ligeramente para evitar z-fighting con el fondo
      waterMesh.position.y = 0.001;

      // Ajuste final de posición Z para todos los tipos
      waterMesh.position.z += halfLength;

      return waterMesh;
    },
    []
  );

  // --- **MOVIDO setupLighting ANTES de buildCanal** ---
  const setupLighting = useCallback(
    (scene: THREE.Scene, targetGroup: THREE.Group | null) => {
      let sun = scene.getObjectByName("sunLight") as THREE.DirectionalLight;
      let ambientLight = scene.getObjectByName(
        "ambientLight"
      ) as THREE.AmbientLight;
      const lightIntensity = 1.5;
      const ambientIntensity = isDarkMode ? 0.6 : 0.8;

      // Sun Light
      if (!sun) {
        sun = new THREE.DirectionalLight(0xffffff, lightIntensity);
        sun.name = "sunLight";
        sun.castShadow = true;
        sun.shadow.mapSize.width = 2048;
        sun.shadow.mapSize.height = 2048;
        sun.shadow.bias = -0.001;
        sun.shadow.radius = 1;
        scene.add(sun);
        scene.add(sun.target);
      }
      sun.intensity = lightIntensity;
      sun.position.set(canalLength * 0.4, canalDepth * 4, canalWidth * 1.5);

      if (targetGroup) {
        // Update target and shadow camera
        sun.target = targetGroup;
        const bbox = new THREE.Box3().setFromObject(targetGroup);
        const size = bbox.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        const camSize = maxDim > 1 ? maxDim * 1.2 : 10;
        sun.shadow.camera.left = -camSize / 2;
        sun.shadow.camera.right = camSize / 2;
        sun.shadow.camera.top = camSize / 2;
        sun.shadow.camera.bottom = -camSize / 2;
        sun.shadow.camera.near = 0.1;
        sun.shadow.camera.far = maxDim * 3 + 50;
        sun.shadow.camera.updateProjectionMatrix();
      } else {
        sun.target.position.set(0, 0, 0);
      }

      // Ambient Light
      if (!ambientLight) {
        ambientLight = new THREE.AmbientLight(0xffffff, ambientIntensity);
        ambientLight.name = "ambientLight";
        scene.add(ambientLight);
      } else {
        ambientLight.intensity = ambientIntensity;
      }
    },
    [isDarkMode, canalLength, canalDepth, canalWidth]
  ); // Dependencias de setupLighting

  // --- Función Principal de Construcción 3D ---
  const buildCanal = useCallback(() => {
    if (!sceneRef.current) return;
    const scene = sceneRef.current;
    disposeScene(canalGroupRef.current);
    canalGroupRef.current = new THREE.Group();
    canalGroupRef.current.name = "canalGroup";
    scene.add(canalGroupRef.current);

    // --- Materiales ---
    const canalMaterial = new THREE.MeshStandardMaterial({
      color: isDarkMode ? 0x6c757d : 0xaaaaaa,
      metalness: 0.4,
      roughness: 0.7,
      side: THREE.FrontSide, // Usar FrontSide para el canal sólido
      polygonOffset: true,
      polygonOffsetFactor: 1.0,
      polygonOffsetUnits: 1.0,
    });

    const waterMaterial = new THREE.MeshStandardMaterial({
      color: 0x2196f3,
      transparent: true,
      opacity: 0.75,
      metalness: 0.1,
      roughness: 0.15,
      side: THREE.DoubleSide, // DoubleSide puede ser útil para el agua
    });

    // --- Parámetros y Variables ---
    const currentWallThickness = wallThickness;
    const currentWaterDepth = actualWaterDepth; // Usamos el calculado/input
    let currentPosZ = -canalLength / 2; // Posición inicial en Z

    // Helper para interpolación lineal
    const lerp = (start: number, end: number, t: number): number => {
      return start * (1 - t) + end * t;
    };

    // --- Construcción de Segmentos ---
    const params1: ChannelSegmentParams = {
      shape: canalShape,
      width: canalWidth,
      depth: canalDepth,
      sideSlope: sideSlope,
      wallThickness: currentWallThickness,
    };

    if (!enableTransition || transitionLength <= 1e-6) {
      // --- CASO 1: Canal Uniforme (sin transición) ---
      const segment = buildChannelSegment(
        params1,
        canalLength,
        canalMaterial.clone()
      );
      segment.position.z = currentPosZ + canalLength / 2;
      canalGroupRef.current.add(segment);

      // Calcular el tirante normal para el canal uniforme
      let uniformWaterDepth: number;

      if (inputMode === "caudal" && dischargeInput > 0) {
        // Calcular el tirante normal para el canal uniforme
        const uniformNormalDepth = solveNormalDepth(
          dischargeInput,
          params1.shape === "triangular" ? 0 : params1.width,
          params1.shape === "rectangular" ? 0 : params1.sideSlope,
          canalSlope,
          roughness,
          params1.shape
        );

        // Limitar el tirante calculado a la profundidad del canal
        uniformWaterDepth = Math.min(uniformNormalDepth, params1.depth);
      } else {
        // Si no estamos en modo caudal, usar el tirante especificado
        uniformWaterDepth = Math.min(currentWaterDepth, params1.depth);
      }

      const water = buildWaterSegment(
        params1,
        uniformWaterDepth,
        canalLength,
        waterMaterial.clone()
      );

      if (water) {
        water.position.z = currentPosZ + canalLength / 2;
        canalGroupRef.current.add(water);
      }
    } else {
      // --- CASO 2: Canal con Transición ---
      const params2: ChannelSegmentParams = {
        shape: endCanalShape,
        width: endCanalWidth,
        depth: endCanalDepth,
        sideSlope: endSideSlope,
        wallThickness: currentWallThickness, // Asumimos mismo espesor
      };

      const startT = Math.max(0, transitionStart);
      const lengthT = Math.max(0, transitionLength);

      // Longitudes de cada sección
      const len1 = Math.min(startT, canalLength);
      const len2 = Math.min(lengthT, canalLength - len1);
      const len3 = Math.max(0, canalLength - len1 - len2);

      // 1. Segmento Inicial (antes de la transición)
      if (len1 > 1e-6) {
        const segment1 = buildChannelSegment(
          params1,
          len1,
          canalMaterial.clone()
        );
        segment1.position.z = currentPosZ + len1 / 2;
        canalGroupRef.current.add(segment1);

        // Calcular el tirante normal para el segmento inicial
        let initialWaterDepth: number;

        if (inputMode === "caudal" && dischargeInput > 0) {
          // Calcular el tirante normal para el segmento inicial
          const initialNormalDepth = solveNormalDepth(
            dischargeInput,
            params1.shape === "triangular" ? 0 : params1.width,
            params1.shape === "rectangular" ? 0 : params1.sideSlope,
            canalSlope,
            roughness,
            params1.shape
          );

          // Limitar el tirante calculado a la profundidad del canal
          initialWaterDepth = Math.min(initialNormalDepth, params1.depth);
        } else {
          // Si no estamos en modo caudal, usar el tirante especificado
          initialWaterDepth = Math.min(currentWaterDepth, params1.depth);
        }

        const water1 = buildWaterSegment(
          params1,
          initialWaterDepth,
          len1,
          waterMaterial.clone()
        );

        if (water1) {
          water1.position.z = currentPosZ + len1 / 2;
          canalGroupRef.current.add(water1);
        }

        currentPosZ += len1; // Avanzar la posición Z
      }

      // 2. Segmento de Transición (subdividido)
      if (len2 > 1e-6) {
        // Aumentamos el número de pasos para transiciones más realistas
        const numTransitionSteps = Math.max(
          1,
          Math.min(100, Math.round(len2 * 10))
        ); // Más pasos para transiciones largas, mínimo 1
        const stepLength = len2 / numTransitionSteps;

        // Calcular resultados hidráulicos al inicio y final de la transición
        // para futura implementación de visualización de resultados
        /*
        // Resultados al inicio de la transición
        const initialHydraulics = calculateHydraulics(
          Math.min(currentWaterDepth, params1.depth),
          params1.shape === "triangular" ? 0 : params1.width,
          params1.shape === "rectangular" ? 0 : params1.sideSlope,
          canalSlope,
          roughness,
          params1.shape
        );

        // Resultados al final de la transición
        const finalHydraulics = calculateHydraulics(
          Math.min(currentWaterDepth, params2.depth),
          params2.shape === "triangular" ? 0 : params2.width,
          params2.shape === "rectangular" ? 0 : params2.sideSlope,
          canalSlope,
          roughness,
          params2.shape
        );
        */

        // TODO: Implementar visualización de resultados hidráulicos en puntos clave

        for (let i = 0; i < numTransitionSteps; i++) {
          const t = (i + 0.5) / numTransitionSteps; // Interpolación en el punto medio

          // Interpolar parámetros geométricos (linealmente)
          const stepWidth = lerp(params1.width, params2.width, t);
          const stepDepth = lerp(params1.depth, params2.depth, t);
          const stepSideSlope = lerp(params1.sideSlope, params2.sideSlope, t);

          // Determinar la forma para este paso con transición más suave
          let stepShape: CanalShape = params1.shape;
          if (params1.shape !== params2.shape) {
            // Si una forma es triangular, usar trapezoidal para la transición
            if (
              params1.shape === "triangular" ||
              params2.shape === "triangular"
            ) {
              stepShape = "trapezoidal";
            }
            // Si una forma es rectangular y otra trapezoidal, usar trapezoidal
            else if (
              params1.shape === "rectangular" &&
              params2.shape === "trapezoidal"
            ) {
              // Transición más suave entre rectangular y trapezoidal
              stepShape = t < 0.5 ? "rectangular" : "trapezoidal";
            } else if (
              params1.shape === "trapezoidal" &&
              params2.shape === "rectangular"
            ) {
              // Transición más suave entre trapezoidal y rectangular
              stepShape = t < 0.5 ? "trapezoidal" : "rectangular";
            }
          }

          // Ajustar parámetros según la forma
          const effWidth = stepShape === "triangular" ? 0 : stepWidth;
          const effSlope = stepShape === "rectangular" ? 0 : stepSideSlope;

          const stepParams: ChannelSegmentParams = {
            shape: stepShape,
            width: effWidth,
            depth: stepDepth,
            sideSlope: effSlope,
            wallThickness: currentWallThickness,
          };

          // Construir el sub-segmento del canal
          const segmentStep = buildChannelSegment(
            stepParams,
            stepLength,
            canalMaterial.clone()
          );
          segmentStep.position.z = currentPosZ + stepLength / 2;
          canalGroupRef.current.add(segmentStep);

          // Calcular el tirante normal para este segmento de transición
          // basado en los parámetros geométricos interpolados
          let waterDepthInStep: number;

          if (inputMode === "caudal" && dischargeInput > 0) {
            // Calcular el tirante normal para este segmento específico
            const stepNormalDepth = solveNormalDepth(
              dischargeInput,
              effWidth,
              effSlope,
              canalSlope,
              roughness,
              stepShape
            );

            // Limitar el tirante calculado a la profundidad del canal en este punto
            waterDepthInStep = Math.min(stepNormalDepth, stepDepth);
          } else {
            // Si no estamos en modo caudal, interpolar linealmente entre los tirantes
            waterDepthInStep = Math.min(
              lerp(
                Math.min(currentWaterDepth, params1.depth),
                Math.min(currentWaterDepth, params2.depth),
                t
              ),
              stepDepth
            );
          }

          // Construir el sub-segmento del agua con la profundidad calculada
          const waterStep = buildWaterSegment(
            stepParams,
            waterDepthInStep,
            stepLength,
            waterMaterial.clone()
          );

          if (waterStep) {
            waterStep.position.z = currentPosZ + stepLength / 2;
            canalGroupRef.current.add(waterStep);
          }

          currentPosZ += stepLength; // Avanzar la posición Z
        }
      }

      // 3. Segmento Final (después de la transición)
      if (len3 > 1e-6) {
        const segment3 = buildChannelSegment(
          params2,
          len3,
          canalMaterial.clone()
        );
        segment3.position.z = currentPosZ + len3 / 2;
        canalGroupRef.current.add(segment3);

        // Calcular el tirante normal para el segmento final
        let finalWaterDepth: number;

        if (inputMode === "caudal" && dischargeInput > 0) {
          // Calcular el tirante normal para el segmento final
          const finalNormalDepth = solveNormalDepth(
            dischargeInput,
            params2.shape === "triangular" ? 0 : params2.width,
            params2.shape === "rectangular" ? 0 : params2.sideSlope,
            canalSlope,
            roughness,
            params2.shape
          );

          // Limitar el tirante calculado a la profundidad del canal
          finalWaterDepth = Math.min(finalNormalDepth, params2.depth);
        } else {
          // Si no estamos en modo caudal, usar el tirante especificado
          finalWaterDepth = Math.min(currentWaterDepth, params2.depth);
        }

        const water3 = buildWaterSegment(
          params2,
          finalWaterDepth,
          len3,
          waterMaterial.clone()
        );

        if (water3) {
          water3.position.z = currentPosZ + len3 / 2;
          canalGroupRef.current.add(water3);
        }
      }
    }

    // Llamar a setupLighting DESPUÉS de construir toda la geometría
    setupLighting(scene, canalGroupRef.current);
  }, [
    canalShape,
    canalWidth,
    canalDepth,
    canalLength,
    sideSlope,
    wallThickness,
    actualWaterDepth,
    isDarkMode,
    enableTransition,
    transitionStart,
    transitionLength,
    endCanalShape,
    endCanalWidth,
    endCanalDepth,
    endSideSlope,
    buildChannelSegment,
    buildWaterSegment,
    setupLighting,
    inputMode,
    dischargeInput,
    canalSlope,
    roughness,
  ]);

  // --- Efecto de Inicialización de Three.js ---
  useEffect(() => {
    if (!mountRef.current) return;
    console.log("Inicializando Escena Three.js...");
    const mount = mountRef.current;
    let isMounted = true;
    const scene = new THREE.Scene();
    // Usar el mismo color que se usa en el efecto de actualización
    scene.background = new THREE.Color(isDarkMode ? 0x000000 : 0xf0f0f0);
    sceneRef.current = scene;
    const camera = new THREE.PerspectiveCamera(
      50,
      mount.clientWidth / mount.clientHeight,
      0.1,
      1000
    );
    const initialDist = Math.max(canalWidth, canalDepth, canalLength, 10) * 1.5;
    camera.position.set(
      initialDist * 0.6,
      initialDist * 0.5,
      initialDist * 0.6
    );
    cameraRef.current = camera;
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: false });
    renderer.setSize(mount.clientWidth, mount.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.setPixelRatio(window.devicePixelRatio);
    rendererRef.current = renderer;

    // Almacenar la referencia al canvas para la generación de PDF
    canvasRef.current = renderer.domElement;

    mount.appendChild(renderer.domElement);
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.1;
    controls.target.set(0, canalDepth / 4, 0);
    controlsRef.current = controls;
    controls.update();

    // Add event listener for adjusting camera target
    const handleAdjustTarget = (event: Event) => {
      if (controlsRef.current) {
        // @ts-ignore
        const { x, y, z } = (event as CustomEvent).detail as {
          x: number;
          y: number;
          z: number;
        };
        controlsRef.current.target.set(x, y, z);
        controlsRef.current.update();
      }
    };

    window.addEventListener(
      "adjustTarget",
      handleAdjustTarget as EventListener
    );

    const initialCanalGroup = new THREE.Group();
    initialCanalGroup.name = "canalGroup";
    canalGroupRef.current = initialCanalGroup;
    scene.add(initialCanalGroup);
    const gridHelper = new THREE.GridHelper(100, 20, 0x888888, 0x444444);
    gridHelper.name = "grid";
    gridHelper.visible = showGrid;
    scene.add(gridHelper);
    const axesHelper = new THREE.AxesHelper(
      Math.max(canalWidth, canalDepth, 5)
    );
    axesHelper.name = "axes";
    axesHelper.position.y = -0.01;
    axesHelper.visible = showAxes;
    scene.add(axesHelper);
    buildCanal();
    const animate = () => {
      if (!isMounted) return;
      requestRef.current = requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();
    const handleResize = () => {
      if (
        !isMounted ||
        !mountRef.current ||
        !cameraRef.current ||
        !rendererRef.current
      )
        return;
      const w = mountRef.current.clientWidth;
      const h = mountRef.current.clientHeight;
      rendererRef.current.setSize(w, h);
      cameraRef.current.aspect = w / h;
      cameraRef.current.updateProjectionMatrix();
    };
    window.addEventListener("resize", handleResize);
    return () => {
      console.log("Limpiando escena Three.js...");
      isMounted = false;
      if (requestRef.current) cancelAnimationFrame(requestRef.current);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener(
        "adjustTarget",
        handleAdjustTarget as EventListener
      );
      controls?.dispose();
      if (sceneRef.current) {
        disposeScene(sceneRef.current.getObjectByName("grid") ?? null);
        disposeScene(sceneRef.current.getObjectByName("axes") ?? null);
        const sun = sceneRef.current.getObjectByName(
          "sunLight"
        ) as THREE.DirectionalLight;
        if (sun) {
          if (sun.target) disposeScene(sun.target);
          disposeScene(sun);
        }
        disposeScene(sceneRef.current.getObjectByName("ambientLight") ?? null);
        disposeScene(canalGroupRef.current);
        canalGroupRef.current = null;
        sceneRef.current = null;
      }
      if (rendererRef.current) {
        // Primero guardar una referencia al domElement
        const domElement = rendererRef.current.domElement;

        // Luego disponer del renderer
        rendererRef.current.dispose();
        rendererRef.current = null;
        canvasRef.current = null; // Limpiar la referencia al canvas

        // Finalmente, intentar quitar el domElement del DOM si existe y tiene un padre
        if (domElement && domElement.parentNode) {
          try {
            domElement.parentNode.removeChild(domElement);
          } catch (e) {
            console.error("Error al quitar el canvas del DOM:", e);
          }
        }
      }
      console.log("Limpieza de escena Three.js completada.");
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // --- Efecto para Reconstruir Geometría ---
  useEffect(() => {
    buildCanal();
    if (controlsRef.current) {
      controlsRef.current.target.y = canalDepth / 4;
    }
  }, [buildCanal, canalDepth]);

  // --- Efecto para Actualizar Apariencia (Tema, Helpers) ---
  useEffect(() => {
    if (sceneRef.current) {
      // Usar distintos tonos según el modo del visor:
      // - Negro absoluto en modo oscuro (coincide con el tema general)
      // - Un gris muy claro en modo claro (para contraste)
      sceneRef.current.background = new THREE.Color(
        isDarkMode ? 0x000000 : 0xf0f0f0
      );

      // Actualizar visibilidad de elementos auxiliares
      const grid = sceneRef.current.getObjectByName("grid");
      if (grid) grid.visible = showGrid;

      const axes = sceneRef.current.getObjectByName("axes");
      if (axes) axes.visible = showAxes;

      // Actualizar iluminación según el tema
      setupLighting(sceneRef.current, canalGroupRef.current);
    }
  }, [isDarkMode, showGrid, showAxes, setupLighting]);

  // --- Hook para cerrar dropdown exportar ---
  useEffect(() => {
    /* ... (sin cambios) ... */ const handleClickOutside = (
      event: MouseEvent
    ) => {
      if (
        exportButtonRef.current &&
        !exportButtonRef.current.contains(event.target as Node)
      ) {
        setShowExportMenu(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [exportButtonRef]);

  // --- Resultados Memoizados para UI ---
  const { displayResult, flowRegime } = useMemo(() => {
    /* ... (sin cambios) ... */ const defaultDisplay = {
      y: "0.000",
      V: "0.000",
      Q: "0.000",
      A: "0.000",
      P: "0.000",
      R: "0.000",
      T: "0.000",
      yc: "N/A",
      Fr: "N/A",
      E: "N/A",
    };
    let regime = { text: "N/A", color: "gray" };
    if (!result) return { displayResult: defaultDisplay, flowRegime: regime };
    const display = {
      y: result.y.toFixed(3),
      V: result.V.toFixed(3),
      Q: result.Q.toFixed(3),
      A: result.A.toFixed(3),
      P: result.P.toFixed(3),
      R: result.R.toFixed(3),
      T: result.T.toFixed(3),
      yc: result.yc?.toFixed(3) ?? "N/A",
      Fr: result.Fr?.toFixed(3) ?? "N/A",
      E: result.E?.toFixed(3) ?? "N/A",
    };
    if (
      result.Fr !== undefined &&
      result.yc !== undefined &&
      result.y > MIN_DEPTH_SOLVER
    ) {
      const fr = result.Fr;
      const y = result.y;
      const yc = result.yc;
      const criticalTolerance = 0.05;
      if (
        Math.abs(fr - 1.0) < criticalTolerance ||
        Math.abs(y - yc) < criticalTolerance * yc
      ) {
        regime = { text: "Crítico", color: "yellow" };
      } else if (fr < 1.0) {
        regime = { text: "Subcrítico", color: "blue" };
      } else {
        regime = { text: "Supercrítico", color: "red" };
      }
    } else if (result.Q <= MIN_DEPTH_SOLVER * 0.1) {
      regime = { text: "Sin Flujo", color: "gray" };
    }
    return { displayResult: display, flowRegime: regime };
  }, [result]);

  // --- Funciones UI ---
  const handleViewChange = useCallback((position: [number, number, number]) => {
    /* ... (sin cambios) ... */ if (cameraRef.current && controlsRef.current) {
      const cam = cameraRef.current;
      const ctrl = controlsRef.current;
      const target = ctrl.target.clone();
      cam.position.set(...position);
      cam.lookAt(target);
      ctrl.update();
    }
  }, []);
  const resetView = useCallback(() => {
    /* ... (sin cambios) ... */ const dist =
      Math.max(canalWidth, canalDepth, canalLength, 10) * 1.5;
    handleViewChange([dist * 0.6, dist * 0.5, dist * 0.6]);
    if (controlsRef.current) {
      controlsRef.current.target.set(0, canalDepth / 4, 0);
    }
  }, [handleViewChange, canalWidth, canalDepth, canalLength]);
  const downloadFile = useCallback(
    (content: string | ArrayBuffer, fileName: string, contentType: string) => {
      /* ... (definido antes, sin cambios) ... */ const blob = new Blob(
        [content],
        { type: contentType }
      );
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    },
    []
  );
  const exportModel = useCallback(
    async (format: "obj" | "stl" | "gltf") => {
      /* ... (definido antes, sin cambios) ... */ if (
        !sceneRef.current ||
        !canalGroupRef.current ||
        exporting
      )
        return;
      setExporting(true);
      console.log(`Exportando como ${format.toUpperCase()}...`);
      const groupToExport = canalGroupRef.current.clone(true);
      if (!groupToExport || groupToExport.children.length === 0) {
        console.warn("Canal group empty.");
        setExporting(false);
        return;
      }
      try {
        let extension: string;
        let mimeType: string;
        let output: string | ArrayBuffer;

        switch (format) {
          case "obj": {
            const objExporter = new OBJExporter();
            output = objExporter.parse(groupToExport) as string;
            extension = "obj";
            mimeType = "text/plain";
            break;
          }
          case "stl": {
            const stlExporter = new STLExporter();
            output = stlExporter.parse(groupToExport, {
              binary: true,
            }) as unknown as ArrayBuffer;
            extension = "stl";
            mimeType = "model/stl";
            break;
          }
          case "gltf": {
            const gltfExporter = new GLTFExporter();
            output = (await gltfExporter.parseAsync(groupToExport, {
              binary: true,
            })) as ArrayBuffer;
            extension = "glb";
            mimeType = "model/gltf-binary";
            break;
          }
          default:
            throw new Error("Unsupported");
        }
        const fileName = `canal_${canalShape}_${Date.now()}.${extension}`;
        downloadFile(output, fileName, mimeType);
        console.log(`Exported ${fileName}`);
      } catch (error) {
        console.error("Export error:", error);
        alert(`Error exporting to ${format.toUpperCase()}.`);
      } finally {
        setExporting(false);
      }
    },
    [exporting, canalShape, downloadFile]
  );
  const handleShowDetailedAnalysis = useCallback(() => {
    if (!result) {
      console.warn("No hay resultados para mostrar análisis detallado.");
      return;
    }
    setShowHydraulicDetailedModal(true);
  }, [result]);

  // Función para capturar la imagen del visor 3D
  const handleCapturePNG = useCallback(() => {
    if (!rendererRef.current) {
      console.error("No se puede capturar la imagen: renderer no disponible");
      return;
    }

    try {
      // Guardar estado actual
      const originalSize = new THREE.Vector2();
      rendererRef.current.getSize(originalSize);
      const originalPixelRatio = rendererRef.current.getPixelRatio();

      // Aumentar calidad para la captura
      rendererRef.current.setPixelRatio(
        Math.min(window.devicePixelRatio * 2, 4)
      );

      // Renderizar un frame para aplicar los cambios
      if (sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }

      // Obtener la imagen como dataURL
      const dataURL = rendererRef.current.domElement.toDataURL("image/png");

      // Guardar en localStorage para uso en el PDF
      localStorage.setItem("canalIsometricImage", dataURL);

      // Crear link para descarga
      const link = document.createElement("a");
      link.href = dataURL;
      link.download = `canal-parametrico-${new Date()
        .toISOString()
        .slice(0, 10)}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Restaurar configuración original
      rendererRef.current.setPixelRatio(originalPixelRatio);

      // Renderizar un frame para restaurar calidad original
      if (sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    } catch (error) {
      console.error("Error al capturar la imagen:", error);
      throw error;
    }
  }, [sceneRef, cameraRef, rendererRef]);

  // Función para exportar PDF usando exclusivamente la plantilla de Amphos21
  const handleExportPDF = useCallback(() => {
    if (!result || !rendererRef.current || !canvasRef.current) {
      console.error("No se puede generar el PDF: datos no disponibles");
      return;
    }

    try {
      console.log("Capturando imagen para el PDF de Amphos21...");

      // Renderizar un frame de alta calidad para la captura
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        const originalPixelRatio = rendererRef.current.getPixelRatio();
        rendererRef.current.setPixelRatio(
          Math.min(window.devicePixelRatio * 2, 4)
        );
        rendererRef.current.render(sceneRef.current, cameraRef.current);

        // Capturar la imagen del canvas y guardarla en localStorage para que PdfExporter la use
        const canvas = rendererRef.current.domElement;
        const imgData = canvas.toDataURL("image/png");
        localStorage.setItem("canalIsometricImage", imgData);

        // Restaurar la calidad original del renderizador
        rendererRef.current.setPixelRatio(originalPixelRatio);
        rendererRef.current.render(sceneRef.current, cameraRef.current);

        // Importar dinámicamente el módulo PdfExporter
        import("./PdfExporter")
          .then(async (module) => {
            try {
              // Método directo: Crear un elemento temporal para renderizar el componente
              const tempElement = document.createElement("div");
              tempElement.style.display = "none";
              document.body.appendChild(tempElement);

              // Crear un evento personalizado para capturar la generación del PDF
              const pdfGeneratedEvent = new CustomEvent("pdfGenerated");

              // Renderizar el componente PdfExporter en el elemento temporal
              const ReactDOM = require("react-dom");

              // Crear un componente wrapper que llame directamente a generatePdf
              const PdfWrapper = () => {
                const PdfExporter = module.default;

                React.useEffect(() => {
                  // Ejecutar la generación del PDF después de que el componente se monte
                  setTimeout(async () => {
                    try {
                      // Acceder directamente a la función generatePdf del módulo
                      const pdfModule = new PdfExporter({
                        results: result,
                        shape: canalShape,
                        width: canalWidth,
                        depth: canalDepth,
                        sideSlope: sideSlope,
                        roughness: roughness,
                        slope: canalSlope,
                        canalLength: canalLength,
                      });

                      // Llamar al método generatePdf directamente
                      const pdfResult = await pdfModule.generatePdf();

                      // Mostrar notificación de éxito
                      if (pdfResult) {
                        // Mostrar toast de éxito con información sobre el reporte
                        toast({
                          title: "PDF generado correctamente",
                          description: "El reporte ha sido guardado y está disponible en la sección de Reportes.",
                          variant: "success",
                          duration: 5000,
                        });
                      }

                      // Limpiar después de generar el PDF
                      if (tempElement.parentNode) {
                        ReactDOM.unmountComponentAtNode(tempElement);
                        document.body.removeChild(tempElement);
                      }
                    } catch (error) {
                      console.error("Error al generar el PDF:", error);

                      // Mostrar toast de error
                      toast({
                        title: "Error al generar el PDF",
                        description: "Ocurrió un error al generar el reporte. Por favor, intente nuevamente.",
                        variant: "destructive",
                        duration: 5000,
                      });
                    }
                  }, 100);
                }, []);

                return null;
              };

              // Renderizar el componente wrapper
              ReactDOM.render(<PdfWrapper />, tempElement);
            } catch (error) {
              console.error("Error al generar el PDF:", error);
            }
          })
          .catch((error) => {
            console.error("Error al importar PdfExporter:", error);
          });
      }
    } catch (error) {
      console.error("Error al generar el PDF:", error);
    }
  }, [
    result,
    sceneRef,
    cameraRef,
    rendererRef,
    canvasRef,
    canalShape,
    canalWidth,
    canalDepth,
    canalSlope,
    roughness,
    sideSlope,
    canalLength,
  ]);

  // --- Estado Memoizado para Pasar al Renderizador ---
  const renderState = useMemo(
    () => ({
      // Parámetros básicos del canal (nombres adaptados para CanalParametricoRender)
      shape: canalShape,
      width: canalWidth,
      depth: canalDepth,
      sideSlope,
      wallThickness,

      // Parámetros adicionales
      canalLength,
      canalSlope,
      actualWaterDepth,

      // Parámetros de transición
      enableTransition,
      transitionStart,
      transitionLength,
      endCanalShape,
      endCanalWidth,
      endCanalDepth,
      endSideSlope,

      // Preferencias de UI
      isDarkMode,
    }),
    [
      canalShape,
      canalWidth,
      canalDepth,
      sideSlope,
      wallThickness,
      canalLength,
      canalSlope,
      actualWaterDepth,
      enableTransition,
      transitionStart,
      transitionLength,
      endCanalShape,
      endCanalWidth,
      endCanalDepth,
      endSideSlope,
      isDarkMode,
    ]
  );

  // --- JSX ---
  // Componente de depuración para mostrar el tamaño de la pantalla (solo en desarrollo)
  const DebugScreenSize = () => {
    if (process.env.NODE_ENV !== "development") return null;

    return (
      <div className="fixed bottom-2 right-2 bg-blue-900/70 text-white px-2 py-1 rounded text-xs z-50">
        {windowWidth}px
      </div>
    );
  };

  // Note: Responsive warning removed - now supports all screen sizes

  // En pantallas más grandes, mostrar la herramienta completa
  console.log(
    "Pantalla grande detectada:",
    windowWidth,
    "px. Mostrando herramienta CanalParametrico."
  );

  // Actualizar la referencia del canvas
  const mountScene = () => {
    if (!mountRef.current) return;
    // ... existing code ...

    // Inicializar renderer
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      // ... existing code ...
    });

    // Guardar referencia al canvas
    canvasRef.current = renderer.domElement;

    // ... existing code ...
  };

  return (
    <div className="flex flex-col h-screen bg-black text-white font-sans">
      <DebugScreenSize />
      {/* Layout Principal */}
      <div className="flex flex-1 overflow-hidden">
        {/* Panel de Controles Colapsable */}
        <CollapsibleControlPanel
          // Parámetros del canal
          canalShape={canalShape}
          setCanalShape={setCanalShape}
          canalWidth={canalWidth}
          setCanalWidth={setCanalWidth}
          canalDepth={canalDepth}
          setCanalDepth={setCanalDepth}
          sideSlope={sideSlope}
          setSideSlope={setSideSlope}
          canalLength={canalLength}
          setCanalLength={setCanalLength}
          wallThickness={wallThickness}
          setWallThickness={setWallThickness}
          canalSlope={canalSlope}
          setCanalSlope={setCanalSlope}
          roughness={roughness}
          setRoughness={setRoughness}
          // Modo de entrada y valores
          inputMode={inputMode}
          setInputMode={setInputMode}
          waterDepthInput={waterDepthInput}
          setWaterDepthInput={setWaterDepthInput}
          dischargeInput={dischargeInput}
          setDischargeInput={setDischargeInput}
          // Transición
          enableTransition={enableTransition}
          setEnableTransition={setEnableTransition}
          transitionStart={transitionStart}
          setTransitionStart={setTransitionStart}
          transitionLength={transitionLength}
          setTransitionLength={setTransitionLength}
          endCanalShape={endCanalShape}
          setEndCanalShape={setEndCanalShape}
          endCanalWidth={endCanalWidth}
          setEndCanalWidth={setEndCanalWidth}
          endCanalDepth={endCanalDepth}
          setEndCanalDepth={setEndCanalDepth}
          endSideSlope={endSideSlope}
          setEndSideSlope={setEndSideSlope}
          // Resultados
          actualWaterDepth={actualWaterDepth}
          isDarkMode={isDarkMode}
          className="bg-black border-r border-gray-800"
        />

        {/* Área del Visor 3D y Overlays */}
        <div
          className="flex-1 relative bg-black overflow-hidden"
          role="presentation"
          aria-live="polite"
        >
          {/* Manejo de errores */}
          <ErrorDisplay error={error} setError={setError} />

          {/* Visor 3D */}
          <ErrorBoundary
            fallback={
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 text-white p-4">
                <div className="max-w-md text-center">
                  <h3 className="text-xl font-bold mb-2">
                    Error en el renderizado 3D
                  </h3>
                  <p>
                    Ha ocurrido un error al renderizar el canal. Esto puede
                    deberse a limitaciones del navegador o de la tarjeta
                    gráfica.
                  </p>
                  <button
                    type="button"
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    onClick={() => window.location.reload()}
                  >
                    Recargar página
                  </button>
                </div>
              </div>
            }
          >
            <div ref={mountRef} className="absolute inset-0 w-full h-full" />

            {/* Dimension Annotations */}
            <DimensionAnnotations
              scene={sceneRef.current}
              canalGroupRef={canalGroupRef}
              shape={canalShape}
              width={canalWidth}
              depth={canalDepth}
              sideSlope={sideSlope}
              length={canalLength}
              wallThickness={wallThickness}
              actualWaterDepth={actualWaterDepth}
              visible={showDimensions}
            />
          </ErrorBoundary>

          {/* Side Control Buttons */}
          <SideControlButtons
            result={result}
            handleExportPDF={() => handleExportPDF()}
            handleCapturePNG={() => handleCapturePNG()}
            shape={canalShape}
            width={canalWidth}
            depth={canalDepth}
            sideSlope={sideSlope}
            roughness={roughness}
            slope={canalSlope}
            canalLength={canalLength}
          />

          {/* Controles de visualización en esquina superior derecha */}
          <div className="absolute top-3 right-3 flex flex-col sm:flex-row gap-2 z-20">
            <TooltipProvider>
              <div className="flex gap-2">
                {/* Usar el componente HomeButton en lugar del código directo */}
                <HomeButton />
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={showGrid ? "default" : "secondary"}
                      className={`w-9 h-9 shadow-md ${
                        showGrid
                          ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                          : "bg-gray-800 hover:bg-gray-700 border border-blue-500/20"
                      }`}
                      onClick={() => setShowGrid(!showGrid)}
                    >
                      <LayoutGrid className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">
                      Mostrar/Ocultar Grilla
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={showAxes ? "default" : "secondary"}
                      className={`w-9 h-9 shadow-md ${
                        showAxes
                          ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                          : "bg-gray-800 hover:bg-gray-700 border border-blue-500/20"
                      }`}
                      onClick={() => setShowAxes(!showAxes)}
                    >
                      <AlignRight className="w-4 h-4 rotate-45" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">
                      Mostrar/Ocultar Ejes
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={showDimensions ? "default" : "secondary"}
                      className={`w-9 h-9 shadow-md ${
                        showDimensions
                          ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                          : "bg-gray-800 hover:bg-gray-700 border border-blue-500/20"
                      }`}
                      onClick={() => {
                        // Toggle dimensions visibility
                        setShowDimensions(!showDimensions);

                        // If turning on dimensions, move camera to front view
                        if (!showDimensions) {
                          const H = canalDepth;
                          const dist =
                            Math.max(canalLength, H, canalWidth) * 2.0;
                          // Move to front view (Z-) with offset to see dimensions at the front of the canal
                          handleViewChange([0, H / 2, -dist * 2 - canalLength]);

                          // Wait a bit and then adjust controls target to look at the front face of the canal
                          setTimeout(() => {
                            const targetEvent = new CustomEvent(
                              "adjustTarget",
                              {
                                detail: {
                                  x: 0,
                                  y: H / 2,
                                  z: -canalLength - 0.5,
                                },
                              }
                            );
                            window.dispatchEvent(targetEvent);
                          }, 100);
                        }
                      }}
                    >
                      <Ruler className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">
                      Mostrar/Ocultar Dimensiones
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="default"
                      className="w-9 h-9 shadow-md bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                      onClick={resetView}
                    >
                      <RotateCw className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">Resetear Vista</div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={isDarkMode ? "default" : "secondary"}
                      className={`w-9 h-9 shadow-md ${
                        isDarkMode
                          ? "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                          : "bg-gray-800 hover:bg-gray-700 border border-blue-500/20"
                      }`}
                      onClick={() => setIsDarkMode(!isDarkMode)}
                    >
                      {isDarkMode ? (
                        <Sun className="w-4 h-4" />
                      ) : (
                        <Moon className="w-4 h-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">
                      {isDarkMode ? "Modo Claro" : "Modo Oscuro"} (visor)
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
            </TooltipProvider>
            <div className="flex gap-2">
              {handleShowDetailedAnalysis && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="secondary"
                      className="w-9 h-9 shadow-md bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                      onClick={handleShowDetailedAnalysis}
                    >
                      <ListChecks className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-gray-900/95 backdrop-blur-md border border-blue-900/30 shadow-lg rounded-lg p-2"
                  >
                    <div className="text-sm text-white">Análisis Detallado</div>
                  </TooltipContent>
                </Tooltip>
              )}

              <Select
                defaultValue="select"
                onValueChange={(value: string) => {
                  if (value === "select") return;
                  const view = value;
                  const H = canalDepth;
                  const dist = Math.max(canalLength, H, canalWidth) * 2.0;
                  const targetOffsetZ = enableTransition
                    ? -canalLength / 2 + transitionStart + transitionLength / 2
                    : 0;
                  const targetY = H / 3;

                  if (view === "front") handleViewChange([0, targetY, dist]);
                  else if (view === "back")
                    handleViewChange([0, targetY, -dist]);
                  else if (view === "top")
                    handleViewChange([0, dist, targetOffsetZ]);
                  else if (view === "bottom")
                    handleViewChange([0, -dist, targetOffsetZ]);
                  else if (view === "left")
                    handleViewChange([-dist, targetY, targetOffsetZ]);
                  else if (view === "right")
                    handleViewChange([dist, targetY, targetOffsetZ]);
                  else if (view === "iso") resetView();
                }}
              >
                <SelectTrigger
                  className={cn(
                    "w-[140px] h-9 text-xs",
                    isDarkMode
                      ? "bg-blue-900/30 text-white border border-blue-500/20"
                      : "bg-white/90 text-black border-blue-500/30 font-medium"
                  )}
                >
                  <SelectValue placeholder="Vista Rápida" />
                </SelectTrigger>
                <SelectContent
                  className={cn(
                    "border-blue-900/30 backdrop-blur-sm",
                    isDarkMode
                      ? "bg-gray-900/95 text-white"
                      : "bg-white text-black shadow-lg"
                  )}
                >
                  <SelectItem
                    value="select"
                    disabled
                    className={
                      isDarkMode ? "text-muted-foreground" : "text-gray-500"
                    }
                  >
                    Vista Rápida
                  </SelectItem>
                  <SelectItem value="front">Frontal (Z+)</SelectItem>
                  <SelectItem value="back">Posterior (Z-)</SelectItem>
                  <SelectItem value="top">Superior (Y+)</SelectItem>
                  <SelectItem value="bottom">Inferior (Y-)</SelectItem>
                  <SelectItem value="left">Izquierda (X-)</SelectItem>
                  <SelectItem value="right">Derecha (X+)</SelectItem>
                  <SelectItem value="iso">Isométrica</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Menú de exportación */}
          <ExportMenu
            exporting={exporting}
            showExportMenu={showExportMenu}
            setShowExportMenu={setShowExportMenu}
            exportModel={exportModel}
            exportButtonRef={exportButtonRef}
            isDarkMode={isDarkMode}
          />

          {/* Resultados hidráulicos mejorados */}
          <EnhancedResultsDisplay
            results={{
              y: displayResult.y,
              yc: displayResult.yc,
              A: displayResult.A,
              P: displayResult.P,
              R: displayResult.R,
              V: displayResult.V,
              Q: displayResult.Q,
              E: displayResult.E,
              Fr: displayResult.Fr,
            }}
            flowRegime={flowRegime}
          />
        </div>
      </div>

      {/* Modal de análisis detallado */}
      {result && (
        <EngineeringDetailedModal
          showModal={showHydraulicDetailedModal}
          setShowModal={setShowHydraulicDetailedModal}
          results={result}
          shape={canalShape}
          width={canalWidth}
          depth={canalDepth}
          sideSlope={sideSlope}
          roughness={roughness}
          slope={canalSlope}
          canalLength={canalLength}
          energyProfileData={null}
        />
      )}
    </div>
  );
};

export default CanalParametrico;
