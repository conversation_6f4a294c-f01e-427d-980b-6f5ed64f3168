// Global type declarations for HYDRA21 application

declare global {
  interface Window {
    __TAURI__?: any;
    __TAURI_METADATA__?: any;
  }
}

// Tauri API types
declare module '@tauri-apps/api' {
  export * from '@tauri-apps/api/app';
  export * from '@tauri-apps/api/event';
  export * from '@tauri-apps/api/fs';
  export * from '@tauri-apps/api/http';
  export * from '@tauri-apps/api/notification';
  export * from '@tauri-apps/api/os';
  export * from '@tauri-apps/api/path';
  export * from '@tauri-apps/api/process';
  export * from '@tauri-apps/api/shell';
  export * from '@tauri-apps/api/tauri';
  export * from '@tauri-apps/api/updater';
  export * from '@tauri-apps/api/window';
}

// React types extension
declare module 'react' {
  interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    'data-tauri-drag-region'?: boolean;
  }
}

// Next.js types extension
declare module 'next/navigation' {
  export function usePathname(): string;
  export function useRouter(): any;
  export function useSearchParams(): URLSearchParams;
}

// Chart.js types
declare module 'chart.js' {
  export * from 'chart.js/auto';
}

// Three.js types
declare module 'three' {
  export * from 'three/src/Three';
}

// D3 types
declare module 'd3' {
  export * from 'd3-selection';
  export * from 'd3-scale';
  export * from 'd3-axis';
  export * from 'd3-shape';
  export * from 'd3-array';
}

// WebXR types
declare module 'webxr' {
  interface XRSession {
    requestReferenceSpace(type: string): Promise<XRReferenceSpace>;
    requestAnimationFrame(callback: XRFrameRequestCallback): number;
    end(): Promise<void>;
  }
  
  interface XRReferenceSpace {
    getOffsetReferenceSpace(originOffset: XRRigidTransform): XRReferenceSpace;
  }
  
  interface XRRigidTransform {
    position: DOMPointReadOnly;
    orientation: DOMPointReadOnly;
  }
  
  type XRFrameRequestCallback = (time: DOMHighResTimeStamp, frame: XRFrame) => void;
  
  interface XRFrame {
    session: XRSession;
    getViewerPose(referenceSpace: XRReferenceSpace): XRViewerPose | null;
  }
  
  interface XRViewerPose {
    transform: XRRigidTransform;
    views: XRView[];
  }
  
  interface XRView {
    eye: 'left' | 'right' | 'none';
    projectionMatrix: Float32Array;
    transform: XRRigidTransform;
  }
}

export {};
