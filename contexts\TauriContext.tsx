"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import statisticsService from "@/lib/statistics-service";
import { usePathname } from "next/navigation";
import { invoke } from "@/lib/tauri-simple";

// Define the context type
interface TauriContextType {
  isTauri: boolean;
  isLoading: boolean;
  error: Error | null;
  appVersion: string | null;
  checkAppStatus: () => Promise<string>;
  getSystemInfo: () => Promise<any>;
  trackFeatureUsage: (feature: string, duration?: number) => Promise<void>;
  trackSectionVisit: (section: string) => Promise<void>;
}

// Create the context with default values
const TauriContext = createContext<TauriContextType>({
  isTauri: false,
  isLoading: true,
  error: null,
  appVersion: null,
  checkAppStatus: async () => "",
  getSystemInfo: async () => ({}),
  trackFeatureUsage: async () => {},
  trackSectionVisit: async () => {},
});

// Hook to use the Tauri context
export const useTauri = () => useContext(TauriContext);

// Provider component
export const TauriProvider = ({ children }: { children: ReactNode }) => {
  const [isTauri, setIsTauri] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [appVersion, setAppVersion] = useState<string | null>(null);
  const pathname = usePathname();

  // Initialize Tauri API
  useEffect(() => {
    const initTauri = async () => {
      try {
        console.log("Initializing Tauri context...");

        // Force show content to prevent blank screen
        if (typeof document !== 'undefined') {
          document.body.style.visibility = 'visible';
          document.body.style.opacity = '1';

          const appContent = document.getElementById('app-content');
          if (appContent) {
            appContent.style.opacity = '1';
            appContent.style.visibility = 'visible';
          }
          console.log("Forced content visibility in TauriContext");
        }

        // Check if we're in Tauri environment
        const inTauri = !!(
          typeof window !== "undefined" && window.__TAURI__
        );

        if (inTauri) {
          console.log("🖥️ Tauri environment detected");
          setIsTauri(true);

          try {
            // Get system info
            const systemInfo = await invoke("get_system_info");
            console.log("System info obtained:", systemInfo);
            
            if (systemInfo && systemInfo.version) {
              setAppVersion(systemInfo.version);
            }

            // Initialize statistics service
            await statisticsService.initialize();
            console.log("Statistics service initialized");

            // Start system monitoring with 30 second interval
            await statisticsService.startSystemMonitoring(30000);
            console.log("System monitoring started");

            console.log("✅ Tauri initialized successfully");
          } catch (invokeError) {
            console.error("Error during Tauri initialization:", invokeError);
            setError(new Error("Failed to initialize Tauri features"));
          }
        } else {
          console.log("📱 Web environment detected");
          setIsTauri(false);
          setAppVersion("Web Version");
        }
      } catch (err) {
        console.error("Error initializing Tauri:", err);
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setIsLoading(false);
        console.log("Tauri context initialization complete");
      }
    };

    initTauri();
  }, []);

  // Track section visits based on pathname changes
  useEffect(() => {
    if (pathname && !isLoading && isTauri) {
      // Extract section name from pathname
      const pathParts = pathname.split("/").filter(Boolean);
      const section =
        pathParts.length > 0 ? pathParts[pathParts.length - 1] : "dashboard";

      // Convert section name to a more readable format
      const sectionMap: Record<string, string> = {
        dashboard: "Dashboard",
        estadisticas: "Estadísticas",
        reportes: "Reportes",
        ajustes: "Ajustes",
        ayuda: "Ayuda",
        // Add more mappings as needed
      };

      const sectionName = sectionMap[section] || section;

      // Track the section visit
      statisticsService.trackSectionVisit(sectionName).catch(error => {
        console.error("Error tracking section visit:", error);
      });
    }
  }, [pathname, isLoading, isTauri]);

  // Function to check app status
  const checkAppStatus = async (): Promise<string> => {
    if (!isTauri) {
      return "Web version - no status check available";
    }

    try {
      const status = await invoke("check_app_status");
      return status as string;
    } catch (err) {
      console.error("Error checking app status:", err);
      throw err;
    }
  };

  // Function to get system info
  const getSystemInfo = async (): Promise<any> => {
    if (!isTauri) {
      return {
        os: "Web Browser",
        os_version: navigator.userAgent,
        arch: "Web",
        hostname: window.location.hostname,
        version: "Web Version",
        app_name: "HYDRA21",
        tauri_version: "N/A",
        rust_version: "N/A",
        ip_addresses: ["N/A"],
        cpu: {
          brand: "Browser CPU",
          usage: 0,
          cores: navigator.hardwareConcurrency || 4,
        },
        memory: {
          total: 0,
          used: 0,
          available: 0,
          usage_percent: 0,
        },
      };
    }

    try {
      console.log("Getting system information...");

      // First get basic system info
      const basicInfo = await invoke("get_system_info");
      console.log("Basic system info obtained:", basicInfo);

      // Then get real-time system resources
      try {
        const systemResources = await statisticsService.getCurrentSystemResources();
        console.log("Real-time system resources obtained:", systemResources);

        // Merge basic info with real-time resources
        return {
          ...basicInfo,
          cpu: systemResources.cpu || basicInfo.cpu,
          memory: systemResources.memory || basicInfo.memory,
          gpu: systemResources.gpu || undefined,
        };
      } catch (resourceError) {
        console.warn("Could not get real-time resources:", resourceError);
        return basicInfo;
      }
    } catch (err) {
      console.error("Error getting system info:", err);
      // Return fallback information
      return {
        os: "Unknown",
        os_version: "Unknown",
        arch: "Unknown",
        hostname: "Unknown",
        version: appVersion || "0.1.0",
        app_name: "HYDRA21",
        tauri_version: "Unknown",
        rust_version: "Unknown",
        ip_addresses: ["Unknown"],
        cpu: {
          brand: "Information not available",
          usage: 0,
          cores: 0,
        },
        memory: {
          total: 0,
          used: 0,
          available: 0,
          usage_percent: 0,
        },
      };
    }
  };

  // Function to track feature usage
  const trackFeatureUsage = async (
    feature: string,
    duration?: number
  ): Promise<void> => {
    if (isTauri) {
      try {
        await statisticsService.trackFeatureUsage(feature, duration);
      } catch (err) {
        console.error(`Error tracking feature usage for ${feature}:`, err);
      }
    } else {
      console.log(`[Web mode] Would track feature usage: ${feature}`);
    }
  };

  // Function to track section visit
  const trackSectionVisit = async (section: string): Promise<void> => {
    if (isTauri) {
      try {
        await statisticsService.trackSectionVisit(section);
      } catch (err) {
        console.error(`Error tracking section visit for ${section}:`, err);
      }
    } else {
      console.log(`[Web mode] Would track section visit: ${section}`);
    }
  };

  return (
    <TauriContext.Provider
      value={{
        isTauri,
        isLoading,
        error,
        appVersion,
        checkAppStatus,
        getSystemInfo,
        trackFeatureUsage,
        trackSectionVisit,
      }}
    >
      {children}
    </TauriContext.Provider>
  );
};