@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar estilos de tema personalizados */
@import "./styles/theme.css";

/* Native desktop window - unified blue theme */
.window-root {
  background: hsl(var(--background)); /* Use theme background */
}

/* Native desktop colors - unified blue theme */
:root {
  --accent-color: #3B82F6; /* Primary blue */
  --card-bg: hsl(var(--card)); /* Use theme card background */
}

/* Asegurarse de que los estilos se apliquen correctamente */
html {
  height: 100%;
  width: 100%;
}

html.dark {
  color-scheme: dark;
}

/* Animaciones para el Hero */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.2;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.95);
  }
}

/* Nuevas animaciones */
@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-y {
  0% {
    background-position: 50% 0%;
  }
  50% {
    background-position: 50% 100%;
  }
  100% {
    background-position: 50% 0%;
  }
}

@keyframes border-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes shimmer-vertical {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

.equation-animation {
  animation: fadeInOut 15s ease-in-out infinite;
  will-change: opacity, transform;
}

.logo-animation {
  animation: pulse 8s ease-in-out infinite;
  will-change: opacity, transform;
}

.static-grid {
  background-image: linear-gradient(
      to right,
      rgba(74, 74, 74, 0.2) 1px,
      transparent 0
    ),
    linear-gradient(to bottom, rgba(74, 74, 74, 0.2) 1px, transparent 0);
  background-repeat: repeat;
  background-size: 50px 50px;
}

.animate-shimmer {
  animation: shimmer 3s infinite;
}

.animate-shimmer-vertical {
  animation: shimmer-vertical 3s infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
  background-size: 200% 100%;
}

.animate-gradient-y {
  animation: gradient-y 15s ease infinite;
  background-size: 100% 200%;
}

.animate-border-pulse {
  animation: border-pulse 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@layer base {
  :root {
    /* Unified Blue Theme - Light Mode */
    --background: 0 0% 100%;
    --foreground: 217 91% 20%; /* Deep blue for text */
    --card: 0 0% 100%;
    --card-foreground: 217 91% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 217 91% 20%;
    --primary: 217 91% 60%; /* Primary blue #3B82F6 */
    --primary-foreground: 0 0% 100%;
    --secondary: 214 100% 50%; /* Secondary blue #0066FF */
    --secondary-foreground: 0 0% 100%;
    --accent: 199 89% 48%; /* Accent blue #0EA5E9 */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%; /* Azul primario para ring */
    --radius: 0.5rem;
    --chart-1: 217 91% 60%; /* Azul primario #3B82F6 */
    --chart-2: 214 100% 50%; /* Azul secundario #0066FF */
    --chart-3: 199 89% 48%; /* Azul acento #0EA5E9 */
    --chart-4: 195 83% 38%; /* Azul verdoso #0D9488 */
    --chart-5: 221 83% 53%; /* Azul púrpura #4F46E5 */

    /* Establecer Rubik como fuente predeterminada */
    font-family: "Rubik", sans-serif;
  }

  .dark {
    /* Unified Blue Theme - Dark Mode */
    --background: 0 0% 0%; /* Pure black background */
    --foreground: 210 40% 98%;
    --card: 217 91% 5%; /* Dark blue cards */
    --card-foreground: 210 40% 98%;
    --popover: 0 0% 0%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 70%; /* Lighter blue for dark theme #60A5FA */
    --primary-foreground: 0 0% 0%;
    --secondary: 214 100% 60%; /* Secondary blue #3B82F6 */
    --secondary-foreground: 0 0% 100%;
    --muted: 217 91% 10%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 199 89% 58%; /* Accent blue #22D3EE */
    --accent-foreground: 0 0% 0%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 91% 15%;
    --input: 217 91% 15%;
    --ring: 217 91% 70%;
    /* Blue-only chart colors for dark theme */
    --chart-1: 217 91% 70%; /* Light blue #60A5FA */
    --chart-2: 214 100% 60%; /* Medium blue #3B82F6 */
    --chart-3: 199 89% 58%; /* Cyan blue #22D3EE */
    --chart-4: 210 100% 55%; /* Deep blue #1D4ED8 */
    --chart-5: 220 100% 65%; /* Purple blue #6366F1 */

    /* Maintain Rubik as default font in dark theme */
    font-family: "Rubik", sans-serif;
  }
}

* {
  @apply border-border;
}

body {
  @apply bg-background text-foreground font-rubik;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted/30;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/40 hover:bg-primary/60 transition-colors;
  border-radius: 5px;
}

/* Native desktop utility classes - no transparency */
@layer components {
  .glass-effect {
    @apply bg-gray-100 border border-gray-300 shadow-sm;
  }

  .dark .glass-effect {
    @apply bg-gray-800 border border-gray-700 shadow-lg;
  }

  .animated-gradient {
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #3b82f6 100%);
    background-size: 200% 100%;
    animation: gradient-x 8s linear infinite;
  }

  .animated-gradient-purple {
    background: linear-gradient(90deg, #4f46e5 0%, #818cf8 50%, #4f46e5 100%);
    background-size: 200% 100%;
    animation: gradient-x 8s linear infinite;
  }

  .animated-border {
    position: relative;
    overflow: hidden;
  }

  .animated-border::after {
    content: "";
    position: absolute;
    inset: 0;
    border: 1px solid transparent;
    border-radius: inherit;
    background: linear-gradient(
        90deg,
        rgba(59, 130, 246, 0) 0%,
        rgba(59, 130, 246, 0.8) 50%,
        rgba(59, 130, 246, 0) 100%
      )
      border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    animation: gradient-x 3s infinite;
    background-size: 200% 100%;
  }

  .interactive-card {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:shadow-blue-500/20;
  }

  .floating-element {
    @apply animate-float;
  }

  /* Nuevas clases de utilidad */
  .bg-grid-dark {
    background-image: linear-gradient(
        to right,
        rgba(74, 74, 74, 0.1) 1px,
        transparent 1px
      ),
      linear-gradient(to bottom, rgba(74, 74, 74, 0.1) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  .bg-gradient-blur {
    position: relative;
    overflow: hidden;
  }

  .bg-gradient-blur::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
      circle at center,
      rgba(59, 130, 246, 0.2) 0%,
      transparent 50%
    );
    opacity: 0.4;
    animation: spin-slow 30s linear infinite;
    z-index: -1;
  }

  .glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    transition: box-shadow 0.3s ease;
  }

  .glow-effect:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }

  .text-gradient-blue {
    @apply text-primary;
  }

  .text-gradient-purple {
    @apply text-secondary;
  }

  .text-gradient-multi {
    @apply text-primary;
  }

  /* Animaciones para el sidebar */
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  .animate-collapsible-down {
    animation: collapsible-down 0.2s ease-out;
  }

  .animate-collapsible-up {
    animation: collapsible-up 0.2s ease-out;
  }

  /* Estilos responsivos para el dashboard */
  .dashboard-content {
    @apply w-full min-h-full;
  }

  /* Breakpoint para dispositivos extra pequeños */
  @media (max-width: 480px) {
    .dashboard-content {
      @apply space-y-4;
    }

    /* Ajustes para tarjetas y grids en móviles */
    .dashboard-content .grid {
      @apply grid-cols-1 gap-3;
    }

    .dashboard-content h1 {
      @apply text-xl;
    }

    .dashboard-content h2 {
      @apply text-lg;
    }

    .dashboard-content .card,
    .dashboard-content .glass-effect {
      @apply p-3;
    }
  }

  /* Breakpoint para tablets */
  @media (min-width: 481px) and (max-width: 768px) {
    .dashboard-content .grid {
      @apply grid-cols-1 md:grid-cols-2 gap-4;
    }

    .dashboard-content {
      @apply space-y-5;
    }
  }

  /* Breakpoint para desktop pequeño */
  @media (min-width: 769px) and (max-width: 1024px) {
    .dashboard-content .grid {
      @apply grid-cols-2 lg:grid-cols-3 gap-5;
    }
  }

  /* Asegurar que el sidebar no interfiera con el contenido en móviles */
  @media (max-width: 767px) {
    .dashboard-main-content {
      @apply w-full;
    }

    .dashboard-layout {
      @apply relative;
    }
  }

  /* Estilos para la navegación */
  .nav-link {
    @apply px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }

  .nav-link:hover {
    @apply bg-primary/10 text-primary;
  }

  .nav-link.active {
    @apply bg-primary/20 text-primary font-semibold;
  }

  /* Estilos para botones */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
    disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary/90;
  }

  .btn-outline {
    @apply border border-input bg-transparent hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-link {
    @apply text-primary underline-offset-4 hover:underline;
  }

  /* Tamaños de botones */
  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }

  .btn-default {
    @apply h-10 py-2 px-4;
  }

  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }

  /* Estilos para tarjetas */
  .card {
    @apply rounded-lg border bg-black/60 text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Nuevos elementos de UI */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary/20 text-primary border border-primary/20;
  }

  .badge-secondary {
    @apply bg-secondary/20 text-secondary border border-secondary/20;
  }

  .badge-success {
    @apply bg-secondary/20 text-secondary border border-secondary/20;
  }

  .badge-warning {
    @apply bg-primary/20 text-primary border border-primary/20;
  }

  .badge-danger {
    @apply bg-destructive/20 text-destructive border border-destructive/20;
  }

  /* Native desktop card effects - no transparency */
  .glass-glow-effect {
    @apply rounded-xl bg-zinc-800 border border-zinc-700 shadow-xl;
  }

  /* Native desktop blue variant */
  .glass-glow-effect-blue {
    @apply rounded-xl bg-zinc-800 border border-blue-600 shadow-xl ring-1 ring-blue-600;
  }

  /* Variante con borde más visible */
  .glass-glow-effect-border {
    @apply rounded-xl bg-zinc-900/80 backdrop-blur-md border border-white/10 shadow-[0_0_20px_#3b82f650] ring-1 ring-white/10;
  }

  /* Clases para personalizar colores del glow - azules */
  .glow-blue {
    @apply shadow-[0_0_20px_#3B82F650]; /* Azul primario */
  }
  .glow-purple {
    @apply shadow-[0_0_20px_#4F46E550]; /* Azul púrpura */
  }
  .glow-green {
    @apply shadow-[0_0_20px_#0D948850]; /* Azul verdoso */
  }
  .glow-red {
    @apply shadow-[0_0_20px_#EF444450]; /* Rojo para alertas */
  }
  .glow-amber {
    @apply shadow-[0_0_20px_#0EA5E950]; /* Azul acento */
  }

  .code-font {
    @apply font-hack text-sm;
  }

  /* Otros estilos para código */
  pre,
  code {
    @apply font-hack;
  }
}

/* Definimos un breakpoint para dispositivos extra pequeños */
@layer utilities {
  .xs\:inline-flex {
    @apply inline-flex;
  }

  .xs\:hidden {
    @apply hidden;
  }

  /* Utilidades para espaciado */
  .space-y-1 {
    @apply flex flex-col gap-1;
  }

  .space-y-2 {
    @apply flex flex-col gap-2;
  }

  .space-y-4 {
    @apply flex flex-col gap-4;
  }

  .space-x-2 {
    @apply flex flex-row gap-2;
  }

  .space-x-4 {
    @apply flex flex-row gap-4;
  }

  /* Utilidades para texto */
  .text-gradient {
    @apply text-primary;
  }

  /* Utilidades para fondos */
  .bg-grid {
    background-size: 100px 100px;
    background-image: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.05) 1px,
        transparent 1px
      ),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  .dark .bg-grid {
    background-image: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.05) 1px,
        transparent 1px
      ),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  /* Nuevas utilidades de fondo */
  .bg-dots {
    background-image: radial-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    );
    background-size: 20px 20px;
  }

  .bg-stripes {
    background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.05) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.05) 75%,
      transparent 75%,
      transparent
    );
    background-size: 40px 40px;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Estilos para la página de autenticación */
body.auth-page {
  background-image: url("/images/bg-pattern.png");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

body.auth-page::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1a1a1a; /* Solid dark background for native desktop */
  z-index: -1;
}

/* Estilos para Clerk en tema oscuro */
.dark {
  /* Ocultar el footer de Clerk y textos de desarrollo */
  .cl-footer,
  .cl-footerAction,
  .cl-footerActionLink,
  .cl-footerText,
  .cl-development-badge,
  .cl-powered-by-clerk,
  .cl-powered-by-clerk-text,
  .cl-powered-by-clerk-logo,
  .cl-badge,
  .cl-badge-text,
  .cl-badge-logo,
  .cl-development-mode,
  .cl-development-mode-text,
  .cl-development-mode-logo,
  .cl-development-mode-badge,
  .cl-development-mode-badge-text,
  .cl-development-mode-badge-logo,
  .cl-powered-by {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  /* Contenedor principal */
  .cl-rootBox {
    width: 100% !important;
    max-width: 100% !important;
  }

  .cl-card {
    background-color: #2a2a2a !important; /* Solid dark background for native desktop */
    border: 1px solid #3b82f6 !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5),
      0 8px 10px -6px rgba(0, 0, 0, 0.3) !important;
  }

  .cl-formButtonPrimary {
    background-color: rgb(59, 130, 246) !important;
  }

  .cl-formButtonPrimary:hover {
    background-color: rgb(37, 99, 235) !important;
  }

  .cl-headerTitle {
    color: white !important;
  }

  .cl-headerSubtitle {
    color: rgb(156, 163, 175) !important;
  }

  .cl-formFieldLabel {
    color: rgb(209, 213, 219) !important;
  }
}

/* Native desktop titlebar styles with theme support */
.titlebar {
  height: 32px;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2147483647;
  border-bottom: 1px solid;
  padding: 0 8px;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

/* Theme-specific titlebar styles - unified blue theme */
.titlebar.dark {
  background: hsl(217 91% 5%);
  border-bottom-color: hsl(217 91% 15%);
  color: hsl(210 40% 98%);
}

.titlebar.light {
  background: hsl(0 0% 100%);
  border-bottom-color: hsl(214.3 31.8% 91.4%);
  color: hsl(217 91% 20%);
}

.titlebar-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 8px;
  cursor: move; /* Indicate draggable area */
}

.app-title {
  font-size: 13px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  letter-spacing: 0.5px;
}

.titlebar-controls {
  display: flex;
  align-items: center;
}

.titlebar-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 46px;
  height: 32px;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

/* Theme-specific button hover states - unified blue theme */
.titlebar.dark .titlebar-button:hover:not(:disabled) {
  background: hsl(217 91% 15%);
}

.titlebar.light .titlebar-button:hover:not(:disabled) {
  background: hsl(217 91% 95%);
}

.titlebar-button:hover:not(:disabled) {
  background: var(--titlebar-button-hover-bg);
}

.titlebar.dark {
  --titlebar-button-hover-bg: hsl(217 91% 15%);
}

.titlebar.light {
  --titlebar-button-hover-bg: hsl(217 91% 95%);
}

.titlebar-button.titlebar-close:hover:not(:disabled) {
  background: #e81123 !important;
  color: #ffffff !important;
}

.titlebar-button svg {
  opacity: 0.9;
  transition: opacity 0.15s ease;
}

.titlebar-button:hover svg {
  opacity: 1;
}

/* Disabled button styling */
.titlebar-button:disabled {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

.titlebar-button:disabled:hover {
  background: transparent !important;
}

/* Ajuste para macOS */
.macos .titlebar {
  justify-content: flex-start;
  padding-left: 16px;
}

/* Ajuste para Linux */
.linux .titlebar button {
  width: 36px;
}

/* Native desktop layout - proper content positioning */
body {
  padding-top: 32px; /* Account for titlebar height */
  margin: 0;
  overflow: hidden; /* Prevent body scroll, let main content handle it */
  position: relative; /* Establish stacking context */
}

/* Ensure main app content fills remaining space */
#app-content {
  height: calc(100vh - 32px); /* Full height minus titlebar */
  overflow: hidden;
}

/* Fix dashboard layout to work within window bounds */
.dashboard-layout {
  height: calc(100vh - 32px); /* Account for titlebar height */
  overflow: hidden;
}

/* Ensure sidebar and main content work together properly */
.dashboard-main-content {
  height: 100%; /* Use full height of dashboard layout */
  overflow-y: auto;
}

/* Fix for notification dropdown positioning issues */
[data-radix-popper-content-wrapper] {
  z-index: 1001 !important;
}

/* Prevent layout shifts when dropdowns open */
.dashboard-layout {
  position: relative;
  isolation: isolate; /* Create new stacking context */
}

/* Ensure dropdown menus don't cause layout shifts */
[data-state="open"] {
  position: relative;
  z-index: auto;
}

/* Fix for notification center dropdown */
.notification-dropdown {
  position: fixed !important;
  z-index: 1001 !important;
}

/* Prevent body scroll and layout shifts when dropdowns are open */
[data-radix-dropdown-menu-content] {
  position: fixed !important;
  z-index: 1001 !important;
  will-change: transform, opacity;
}

/* Ensure proper stacking context for header elements */
header.sticky {
  z-index: 30;
  position: sticky;
  isolation: isolate;
}

/* Z-index hierarchy for modals and dropdowns */
/* TitleBar: 1000 */
/* Notification Dropdown: 1001 */
/* Dialog Overlay: 1002 */
/* Dialog Content: 1003 */
/* Keyboard Shortcuts: 1004 */

/* Ensure all Radix UI components respect our z-index hierarchy */
[data-radix-portal] {
  z-index: 1002 !important;
}

/* Command palette specific fixes */
[data-cmdk-root] {
  z-index: 1003 !important;
}

/* Prevent layout shifts from modal animations */
body:has([data-state="open"]) {
  overflow: hidden;
}

/* Ensure dropdowns appear above everything else */
[data-radix-dropdown-menu-content],
[data-radix-popover-content] {
  z-index: 1001 !important;
}

[data-tauri-drag-region] {
  cursor: move;
}

/* Native desktop scrollbar hiding - maintain functionality but hide visual scrollbars */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit browsers (Chrome, Safari, Edge) */
}

/* Ensure scrolling still works on all elements */
html, body {
  overflow: auto;
}

/* Hide scrollbars but maintain scroll functionality for specific containers */
.scroll-container,
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto,
[class*="overflow-"] {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroll-container::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
.overflow-y-auto::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar,
[class*="overflow-"]::-webkit-scrollbar {
  display: none;
}

/* Global styles for HYDRA21 application */
