"use client";

import React, { useState, useEffect, lazy, Suspense } from "react";
import { DashboardBanner } from "./components/DashboardBanner";
import { DashboardSkeleton } from "./components/DashboardSkeleton";
import { useTauri } from "@/contexts/TauriContext";
import { motion } from "framer-motion";

// Lazy load components to improve performance
const RecentTools = lazy(() => import("./components/RecentTools").then(mod => ({ default: mod.RecentTools })));
const ActiveProjects = lazy(() => import("./components/ActiveProjects").then(mod => ({ default: mod.ActiveProjects })));
const Statistics = lazy(() => import("./components/Statistics").then(mod => ({ default: mod.Statistics })));
const RecentActivity = lazy(() => import("./components/RecentActivity").then(mod => ({ default: mod.RecentActivity })));

// Loading skeleton component
const CardSkeleton = () => (
  <div className="h-full min-h-[16rem] bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 animate-pulse">
    <div className="p-6">
      <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-1/3 mb-4"></div>
      <div className="space-y-3">
        <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-5/6"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-4/6"></div>
      </div>
    </div>
  </div>
);

export default function DashboardPage() {
  const { isTauri } = useTauri();
  // Remove artificial loading state to prevent Fast Refresh issues

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <motion.div
      className="w-full max-w-[1600px] mx-auto"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <motion.div variants={itemVariants} className="mb-6 sm:mb-8">
        <DashboardBanner />
      </motion.div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6"
        variants={containerVariants}
      >
        <motion.div variants={itemVariants} className="w-full">
          <Suspense fallback={<CardSkeleton />}>
            <RecentTools />
          </Suspense>
        </motion.div>

        <motion.div variants={itemVariants} className="w-full">
          <Suspense fallback={<CardSkeleton />}>
            <ActiveProjects />
          </Suspense>
        </motion.div>

        <motion.div variants={itemVariants} className="w-full md:col-span-2 lg:col-span-1">
          <Suspense fallback={<CardSkeleton />}>
            <Statistics />
          </Suspense>
        </motion.div>
      </motion.div>

      <motion.div variants={itemVariants} className="w-full">
        <Suspense fallback={<CardSkeleton />}>
          <RecentActivity />
        </Suspense>
      </motion.div>
    </motion.div>
  );
}
