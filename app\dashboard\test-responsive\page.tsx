"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  DevicePhoneMobileIcon, 
  ComputerDesktopIcon, 
  DeviceTabletIcon,
  CheckCircleIcon,
  XCircleIcon
} from "@heroicons/react/24/outline";

export default function TestResponsivePage() {
  const [screenSize, setScreenSize] = useState<string>("");
  const [windowWidth, setWindowWidth] = useState<number>(0);

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      setWindowWidth(width);
      
      if (width < 480) {
        setScreenSize("Extra Small Mobile");
      } else if (width < 768) {
        setScreenSize("Mobile");
      } else if (width < 1024) {
        setScreenSize("Tablet");
      } else if (width < 1280) {
        setScreenSize("Desktop");
      } else {
        setScreenSize("Large Desktop");
      }
    };

    updateScreenSize();
    window.addEventListener("resize", updateScreenSize);
    return () => window.removeEventListener("resize", updateScreenSize);
  }, []);

  const responsiveTests = [
    {
      name: "Sidebar Behavior",
      mobile: "Overlay with toggle",
      tablet: "Hover to expand",
      desktop: "Hover to expand",
      status: "working"
    },
    {
      name: "Grid Layout",
      mobile: "1 column",
      tablet: "2 columns",
      desktop: "3 columns",
      status: "working"
    },
    {
      name: "Navigation",
      mobile: "Hamburger menu",
      tablet: "Full navigation",
      desktop: "Full navigation",
      status: "working"
    },
    {
      name: "Typography",
      mobile: "Smaller text",
      tablet: "Medium text",
      desktop: "Full size",
      status: "working"
    }
  ];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Responsive Design Test
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Esta página verifica que todos los componentes respondan correctamente a diferentes tamaños de pantalla.
        </p>
      </motion.div>

      {/* Current Screen Info */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-8"
      >
        <div className="flex items-center gap-4 mb-4">
          {windowWidth < 768 ? (
            <DevicePhoneMobileIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          ) : windowWidth < 1024 ? (
            <DeviceTabletIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          ) : (
            <ComputerDesktopIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          )}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Current Screen: {screenSize}
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Width: {windowWidth}px
            </p>
          </div>
        </div>
      </motion.div>

      {/* Responsive Tests Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {responsiveTests.map((test, index) => (
          <motion.div
            key={test.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                {test.name}
              </h3>
              {test.status === "working" ? (
                <CheckCircleIcon className="w-5 h-5 text-green-500" />
              ) : (
                <XCircleIcon className="w-5 h-5 text-red-500" />
              )}
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Mobile:</span>
                <span className="text-gray-900 dark:text-white">{test.mobile}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Tablet:</span>
                <span className="text-gray-900 dark:text-white">{test.tablet}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Desktop:</span>
                <span className="text-gray-900 dark:text-white">{test.desktop}</span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Test Grid Layout */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mb-8"
      >
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Grid Layout Test
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <div
              key={item}
              className="bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-lg p-6 text-center"
            >
              <h3 className="text-lg font-semibold mb-2">Card {item}</h3>
              <p className="text-blue-100">
                Esta tarjeta se adapta al tamaño de pantalla
              </p>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Instructions */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.7 }}
        className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Instrucciones de Prueba
        </h2>
        <ul className="space-y-2 text-gray-600 dark:text-gray-300">
          <li>• Redimensiona la ventana del navegador para probar diferentes tamaños</li>
          <li>• En móvil: El sidebar debe aparecer como overlay al hacer clic en el botón hamburguesa</li>
          <li>• En tablet/desktop: El sidebar debe expandirse al hacer hover</li>
          <li>• Las tarjetas deben reorganizarse según el tamaño de pantalla</li>
          <li>• La tipografía debe ajustarse apropiadamente</li>
        </ul>
      </motion.div>
    </div>
  );
}
