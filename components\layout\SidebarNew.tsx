"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  UserIcon,
  DocumentTextIcon,
  DocumentChartBarIcon,
  ComputerDesktopIcon,
  WrenchScrewdriverIcon,
  CubeTransparentIcon,
  BeakerIcon,
  ArrowTrendingUpIcon,
  SparklesIcon,
  BoltIcon,
  CommandLineIcon,
  CpuChipIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";

interface SidebarProps {
  isOpen: boolean;
}

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  isOpen: boolean;
}

interface SidebarCategoryProps {
  id: string;
  label: string;
  icon: React.ReactNode;
  links: {
    href: string;
    icon: React.ReactNode;
    label: string;
  }[];
  isOpen: boolean;
  pathname: string;
}

const SidebarLink = ({
  href,
  icon,
  label,
  active,
  isOpen,
}: SidebarLinkProps) => {
  // Estado para animación visual
  const [anim, setAnim] = useState(false);

  // Handler para scroll suave si ya está en la ruta
  const pathname = usePathname();
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    if (pathname === href) {
      e.preventDefault();
      setAnim(true);
      window.scrollTo({ top: 0, behavior: "smooth" });
      setTimeout(() => setAnim(false), 500);
    }
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`flex items-center gap-3 px-3 py-2 rounded-lg relative group ${
        active
          ? "bg-blue-600/20 text-blue-400"
          : "text-gray-400 hover:text-blue-300 hover:bg-gray-800/50 dark:hover:bg-gray-800/30"
      } ${
        !isOpen ? "justify-center" : ""
      } transition-all duration-200 ease-out will-change-transform`}
    >
      {active && (
        <motion.div
          layoutId={`active-indicator-${href}`}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-400 rounded-r-full"
        />
      )}
      <motion.div
        animate={anim ? { scale: [1, 1.15, 0.95, 1.08, 1] } : {}}
        transition={{ duration: 0.5 }}
        className={`w-5 h-5 min-w-5 flex-shrink-0 ${
          active ? "text-blue-400" : "text-gray-400 group-hover:text-blue-300"
        } transition-colors duration-200`}
      >
        {icon}
      </motion.div>
      {isOpen && (
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.15, ease: "easeOut" }}
          className="text-sm font-medium whitespace-nowrap overflow-hidden"
        >
          {label}
        </motion.span>
      )}
    </Link>
  );
};

const SidebarCategory = ({
  id,
  label,
  icon,
  links,
  isOpen,
  pathname,
}: SidebarCategoryProps) => {
  // Determine if any link in this category is active
  const isPathInCategory = (path: string) => {
    // For exact matches
    if (links.some((link) => link.href === path)) return true;

    // For nested routes (e.g. /tools/canal-parametrico/something)
    return links.some((link) => path.startsWith(link.href + "/"));
  };

  const hasActiveLink = isPathInCategory(pathname);
  const activeLink = links.find(
    (link) => pathname === link.href || pathname.startsWith(link.href + "/")
  );

  return (
    <div className="mb-1">
      {/* Título de categoría - visible siempre pero con estilos diferentes */}
      <div
        className={`w-full flex items-center px-3 py-2 transition-all duration-200 ${
          hasActiveLink ? "text-blue-400" : "text-gray-400"
        } ${!isOpen ? "justify-center" : ""}`}
      >
        {isOpen ? (
          <div className="flex items-center gap-3 w-full border-b border-blue-900/30 pb-2">
            <div className="w-5 h-5 text-blue-500">{icon}</div>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
              className="text-xs uppercase tracking-wider font-bold text-blue-400/90"
            >
              {label}
            </motion.span>
          </div>
        ) : (
          <Link
            href={activeLink?.href || (links.length > 0 ? links[0].href : "#")}
            className={`flex justify-center ${
              hasActiveLink
                ? "text-blue-400"
                : "text-blue-500 opacity-70 hover:opacity-100"
            }`}
          >
            <div className="w-6 h-6 transition-opacity duration-200">
              {icon}
            </div>
          </Link>
        )}
      </div>

      {/* Enlaces de la categoría - solo visibles cuando está expandido */}
      {isOpen && (
        <div className="pl-2 space-y-1 mt-1">
          <AnimatePresence>
            {links.map((link) => {
              const isActive =
                pathname === link.href || pathname.startsWith(link.href + "/");
              return (
                <motion.div
                  key={link.href}
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  <SidebarLink
                    href={link.href}
                    icon={link.icon}
                    label={link.label}
                    active={isActive}
                    isOpen={isOpen}
                  />
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      )}

      {/* Separador - solo si no es el último elemento */}
      <div className="h-px mx-3 my-3 bg-gray-200 dark:bg-gray-800"></div>
    </div>
  );
};

export function SidebarNew({ isOpen }: SidebarProps) {
  const pathname = usePathname() || "";

  // Categorías para el sidebar con iconos originales de Heroicons
  const categories = [
    {
      id: "ia-mode",
      label: "IA MODE",
      icon: <SparklesIcon className="w-full h-full" />,
      links: [
        {
          href: "/dashboard",
          icon: <CpuChipIcon className="w-full h-full" />,
          label: "IA Features",
        },
      ],
    },
    {
      id: "tools",
      label: "Herramientas",
      icon: <WrenchScrewdriverIcon className="w-full h-full" />,
      links: [
        {
          href: "/tools/canal-parametrico",
          icon: <BeakerIcon className="w-full h-full" />,
          label: "Canal Paramétrico",
        },
        {
          href: "/tools/pipe-design",
          icon: <CommandLineIcon className="w-full h-full" />,
          label: "Diseño de Tuberías",
        },
        {
          href: "/tools/canal-parametrico/visualizador-3d",
          icon: <CubeTransparentIcon className="w-full h-full" />,
          label: "Visualizador 3D",
        },
        {
          href: "/tools/canal-parametrico/caudales",
          icon: <ArrowTrendingUpIcon className="w-full h-full" />,
          label: "Cálculo de Caudales",
        },
      ],
    },
    {
      id: "utils",
      label: "Utilidades",
      icon: <DocumentChartBarIcon className="w-full h-full" />,
      links: [
        {
          href: "/dashboard/reportes",
          icon: <DocumentTextIcon className="w-full h-full" />,
          label: "Mis Reportes",
        },
        {
          href: "/dashboard/estadisticas",
          icon: <DocumentChartBarIcon className="w-full h-full" />,
          label: "Estadísticas",
        },
      ],
    },
    {
      id: "settings",
      label: "Configuración",
      icon: <Cog6ToothIcon className="w-full h-full" />,
      links: [
        {
          href: "/dashboard/ajustes",
          icon: <ComputerDesktopIcon className="w-full h-full" />,
          label: "Ajustes",
        },
        {
          href: "/dashboard/test-responsive",
          icon: <ComputerDesktopIcon className="w-full h-full" />,
          label: "Test Responsive",
        },
      ],
    },
  ];

  return (
    <div className="flex flex-col py-2 overflow-y-auto text-foreground dark:text-gray-200 h-full max-h-screen">
      {categories.map((category, index) => (
        <div key={category.id}>
          <SidebarCategory
            id={category.id}
            label={category.label}
            icon={category.icon}
            links={category.links}
            isOpen={isOpen}
            pathname={pathname}
          />
          {/* No mostrar el separador después del último elemento */}
          {index === categories.length - 1 && (
            <div className="h-px mx-3 bg-gray-200 dark:bg-gray-800 hidden"></div>
          )}
        </div>
      ))}
      {/* Footer with company logo */}
      <div className="mt-auto p-4 border-t border-gray-200 dark:border-gray-800 flex flex-col items-center">
        {isOpen ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center w-full max-w-[180px] transition-all duration-300"
          >
            <a
              href="https://www.amphos21.com"
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col items-center w-full hover:opacity-80 transition-opacity"
              title="Visitar sitio web de Amphos21"
            >
              <div className="relative w-full h-12 mb-1">
                <img
                  src="/images/logo_empresa.png"
                  alt="Amphos21 Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-xs text-blue-500 dark:text-blue-400 font-medium mt-1 text-center">
                v0.1.0-alpha
              </div>
            </a>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center w-10 h-10 transition-all duration-300 hover:scale-110 group relative"
          >
            <a
              href="https://www.amphos21.com"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-full h-full"
              title="Visitar sitio web de Amphos21"
            >
              <img
                src="/images/logo_empresa.png"
                alt="Amphos21 Logo"
                className="w-full h-full object-contain"
              />
            </a>
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 whitespace-nowrap z-50">
              Versión 0.1.0-alpha
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
